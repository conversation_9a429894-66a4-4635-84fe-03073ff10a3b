import { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';

const AuthorPitch = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('Browse Request');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showDetailView, setShowDetailView] = useState(false);
  const [selectedPitch, setSelectedPitch] = useState(null);
  const [showPitchForm, setShowPitchForm] = useState(false);
  const [showSubmissionOverview, setShowSubmissionOverview] = useState(false);
  const [showSuccessPage, setShowSuccessPage] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  const [pitchFormData, setPitchFormData] = useState({
    title: '',
    content: '',
    coverImage: null,
    quote: 1000
  });
  const [previewImage, setPreviewImage] = useState(null);
  const [filters, setFilters] = useState({
    contentType: 'All',
    genre: 'All',
    language: 'All',
    payout: 'All',
    deadline: 'All'
  });
  const [formData, setFormData] = useState({
    title: '',
    brief: '',
    contentType: '',
    genre: '',
    deadline: '',
    wordCount: '',
    language: '',
    tags: '',
    guidelines: '',
    requireFirstRights: false,
    showSubmissionCount: false,
    autoCloseDeadline: false
  });

  const browseRequests = [
    {
      id: 1,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Urban Life',
      type: 'Blog',
      payout: '₹500',
      status: 'Open',
      deadline: 'July 19, 2025',
      description: 'Share A Compelling Article About Life In An Indian Metro City — The Chaos, The Quiet Moments, The Everyday Characters, And The Ever-Changing Pace.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Echoes Of The Forgotten',
      publication: 'Inkspire Magazine',
      genre: 'Romance',
      type: 'Story',
      payout: '₹750',
      status: 'Open',
      deadline: 'July 19, 2025',
      description: 'A Haunting Narrative About People, Places, Or Memories That Time Has Left Behind. We\'re Looking For Stories That Blend Nostalgia With Quiet Revelation.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Midnight Chronicles',
      publication: 'Inkspire Magazine',
      genre: 'Fantasy',
      type: 'Story',
      payout: '₹600',
      status: 'Open',
      deadline: 'Jul 10, 2025',
      description: 'Create an epic fantasy tale with Indian mythology elements. Include magical realism and cultural authenticity.',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    }
  ];

  const myPitches = [
    {
      id: 1,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Urban Life',
      type: 'Blog',
      status: 'Accepted',
      deadline: 'May 29, 2025',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Romance',
      type: 'Story',
      status: 'Pending',
      deadline: 'Jul 14, 2025',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Fantasy',
      type: 'Poem',
      status: 'Rejected',
      deadline: 'Mar 23, 2025',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    },
    {
      id: 4,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Science Fiction',
      type: 'e-book',
      status: 'Pending',
      deadline: 'Dec 29, 2024',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg'
    }
  ];

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCloseForm = () => {
    setShowCreateForm(false);
    setFormData({
      title: '',
      brief: '',
      contentType: '',
      genre: '',
      deadline: '',
      wordCount: '',
      language: '',
      tags: '',
      guidelines: '',
      requireFirstRights: false,
      showSubmissionCount: false,
      autoCloseDeadline: false
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission here
    handleCloseForm();
  };

  const handleViewDetails = (pitch) => {
    setSelectedPitch(pitch);
    setShowDetailView(true);
    setShowCreateForm(false);
  };

  const handleBackToList = () => {
    setShowDetailView(false);
    setSelectedPitch(null);
  };

  // Create a ref for the submission overview section
  const submissionOverviewRef = useRef(null);

  const handlePitchClick = () => {
    setShowSubmissionOverview(true);
    setShowDetailView(false);
    
    // Add a small delay to ensure the component is rendered before scrolling
    setTimeout(() => {
      // Scroll to the top of the page to show the Submission Overview
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }, 100);
  };

  const handleBackFromPitch = () => {
    setShowPitchForm(false);
    setShowSubmissionOverview(true);
  };

  const handleBackFromOverview = () => {
    setShowSubmissionOverview(false);
    setShowDetailView(true);
  };

  const handleProceedToPitch = () => {
    setShowSubmissionOverview(false);
    setShowPitchForm(true);
  };

  const handlePitchFormChange = (field, value) => {
    setPitchFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    const isValid = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isValid) {
      alert('Please upload only JPEG or PNG images');
      return;
    }

    setPitchFormData(prev => ({
      ...prev,
      coverImage: file
    }));
    setPreviewImage(URL.createObjectURL(file));
  };

  // Function to count words, characters, and paragraphs
  const getTextStats = (text) => {
    if (!text || text.trim() === '') {
      return { words: 0, characters: 0, paragraphs: 0 };
    }

    const characters = text.length;
    const words = text.trim().split(/\s+/).filter(word => word.length > 0).length;
    const paragraphs = text.split(/\n\s*\n/).filter(para => para.trim().length > 0).length || 1;

    return { words, characters, paragraphs };
  };

  const handlePitchSubmit = () => {
    console.log('Pitch submitted:', pitchFormData);
    // Handle pitch submission here
    setShowPitchForm(false);
    setShowSubmissionOverview(false);
    setShowDetailView(false);
    setShowSuccessPage(true);
    
    // Scroll to top of page
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  
  const handleBackToHome = () => {
    setShowSuccessPage(false);
  };

  const currentData = activeTab === 'Browse Request' ? browseRequests : myPitches;

  const filteredPitches = currentData.filter(pitch => {
    if (filters.contentType !== 'All' && pitch.type !== filters.contentType) return false;
    if (filters.genre !== 'All' && pitch.genre !== filters.genre) return false;
    if (filters.language !== 'All' && pitch.language !== filters.language) return false;
    if (filters.payout !== 'All' && pitch.payout !== filters.payout) return false;
    if (filters.deadline !== 'All' && pitch.deadline !== filters.deadline) return false;
    return true;
  });

  return (
    <div className="p-4 bg-gray-50 min-h-screen relative overflow-y-auto overflow-x-hidden opacity-0" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
      {/* Success Page */}
      {showSuccessPage && (
        <div className="max-w-3xl mx-auto opacity-0 mt-2" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          <div className="bg-white rounded-3xl shadow-2xl border border-blue-100 overflow-hidden relative">
            {/* Decorative Background Elements */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-indigo-500/10 via-blue-500/5 to-transparent rounded-full blur-3xl"></div>
            
            {/* Success Animation */}
            <div className="flex flex-col items-center justify-center py-10 px-6 text-center relative z-10">
              <div className="w-32 h-32 bg-blue-600 rounded-full flex items-center justify-center mb-6 relative shadow-xl">
                <div className="absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-50" style={{ animationDuration: '2s' }}></div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full animate-pulse opacity-60" style={{ animationDuration: '3s' }}></div>
                <div className="relative z-10 w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-inner">
                  <svg className="w-16 h-16 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={4} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
              
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                Awesome
              </h1>
              
              <h2 className="text-xl font-bold text-gray-800 mb-3">
                Your Pitch Has Been Submitted!
              </h2>
              
              <p className="text-sm text-gray-600 max-w-lg mb-4">
                You'll be notified once your draft is Accepted, Rejected or if Revisions are Requested.
              </p>
              
              <div className="bg-blue-50 border border-blue-200 rounded-2xl p-4 mb-6 max-w-2xl w-full">
                <div className="flex flex-col items-start text-left">
                  <h3 className="font-bold text-gray-800 mb-2 text-sm">You can check the status of your submission in the 'My Submissions' section.</h3>
                  <div className="flex items-center gap-2 text-blue-600 font-medium text-xs">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    <a href="https://skrivee.com/v/evAyuN54x3" className="hover:underline">https://skrivee.com/v/evAyuN54x3</a>
                    <button 
                      onClick={() => {
                        navigator.clipboard.writeText('https://skrivee.com/v/evAyuN54x3')
                          .then(() => {
                            setCopySuccess(true);
                            setTimeout(() => setCopySuccess(false), 2000);
                          })
                          .catch(err => console.error('Failed to copy: ', err));
                      }}
                      className="flex items-center hover:bg-gray-100 p-1 rounded-md transition-colors"
                      title="Copy to clipboard"
                    >
                      {copySuccess ? (
                        <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                        </svg>
                      )}
                    </button>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl w-full">
                <div className="bg-gray-50 border border-gray-200 rounded-2xl p-4">
                  <h3 className="font-bold text-gray-800 mb-2">What's Next?</h3>
                  <p className="text-gray-600 text-sm">The publication will review your draft based on their submission criteria.</p>
                </div>
                
                <div className="bg-gray-50 border border-gray-200 rounded-2xl p-4">
                  <h3 className="font-bold text-gray-800 mb-2">Need Help?</h3>
                  <p className="text-gray-600 text-sm">For questions about submissions, visit our <a href="#" className="text-blue-600 hover:underline">Help Center</a>.</p>
                </div>
              </div>
              
              <button
                onClick={handleBackToHome}
                className="mt-6 px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-700 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-blue-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
                Back to Home
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Header, Tabs and Filters - Hide when create form, detail view, pitch form, submission overview, or success page is shown */}
      {!showCreateForm && !showDetailView && !showPitchForm && !showSubmissionOverview && !showSuccessPage && (
        <>
          {/* Header */}
          <div className="mb-8 relative z-10 opacity-0 -translate-y-4" style={{ animation: 'slideInUp 0.6s ease-out 0.1s forwards' }}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent relative">
              Pitch Hub
              <div className="absolute -bottom-2 left-0 w-15 h-1 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full"></div>
            </h1>
          </div>

          {/* Tabs */}
          <div className="flex gap-2 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.2s forwards' }}>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'Browse Request'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Browse Request')}
            >
              Browse Request
            </button>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'My Pitches'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('My Pitches')}
            >
              My Pitches
            </button>
          </div>

          {/* Subtitle - Only show for My Pitches tab */}
          {activeTab === 'My Pitches' && (
            <p className="text-gray-600 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.3s forwards' }}>
              Track All Your Submitted Pitches To Publications And Businesses
            </p>
          )}

          {/* Filters */}
          <div className="flex gap-3 mb-8 opacity-0 translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.4s forwards' }}>
            <select
              value={filters.contentType}
              onChange={(e) => handleFilterChange('contentType', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Content Type</option>
              <option value="Blog">Blog</option>
              <option value="Story">Story</option>
              <option value="Article">Article</option>
            </select>

            <select
              value={filters.genre}
              onChange={(e) => handleFilterChange('genre', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Genre</option>
              <option value="Romance">Romance</option>
              <option value="Fantasy">Fantasy</option>
              <option value="Urban Life">Urban Life</option>
              <option value="Science Fiction">Science Fiction</option>
              <option value="Environmental">Environmental</option>
            </select>

            <select
              value={filters.language}
              onChange={(e) => handleFilterChange('language', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Language</option>
              <option value="English">English</option>
              <option value="Hindi">Hindi</option>
              <option value="Bengali">Bengali</option>
            </select>

            <select
              value={filters.payout}
              onChange={(e) => handleFilterChange('payout', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Payout</option>
              <option value="₹400">₹400</option>
              <option value="₹500">₹500</option>
              <option value="₹600">₹600</option>
              <option value="₹750">₹750</option>
            </select>

            <select
              value={filters.deadline}
              onChange={(e) => handleFilterChange('deadline', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Deadline</option>
              <option value="May 29, 2025">May 29, 2025</option>
              <option value="Jun 15, 2025">Jun 15, 2025</option>
              <option value="Jul 10, 2025">Jul 10, 2025</option>
              <option value="Jul 14, 2025">Jul 14, 2025</option>
              <option value="Mar 23, 2025">Mar 23, 2025</option>
              <option value="Dec 29, 2024">Dec 29, 2024</option>
            </select>
          </div>
        </>
      )}

      {/* Pitch List */}
      {!showCreateForm && !showDetailView && !showPitchForm && !showSubmissionOverview && !showSuccessPage && (
        <div className="space-y-6">
          {activeTab === 'Browse Request' ? (
            // Card format for Browse Request (same as Pitch page)
            filteredPitches.map((pitch, index) => (
              <div
                key={pitch.id}
                className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 border border-blue-100 shadow-blue-100 hover:border-blue-200 hover:shadow-blue-200 transition-all duration-300 relative overflow-hidden opacity-0 translate-y-8 hover:scale-105 group"
                style={{
                  animationDelay: `${index * 0.1}s`,
                  animation: 'slideInUp 0.6s ease-out forwards',
                  boxShadow: '0 8px 32px rgba(59, 130, 246, 0.08)'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-800"></div>
                <div className="flex justify-between items-start mb-5 relative z-10">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-800 mb-3 leading-tight">{pitch.title}</h3>
                    <div className="flex items-center gap-3">
                      <img
                        src={pitch.avatar}
                        alt={pitch.publication}
                        className="w-10 h-10 rounded-full border-2 border-blue-200 transition-all duration-300 hover:scale-110 hover:border-blue-500"
                      />
                      <span className="font-semibold text-gray-700">{pitch.publication}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-5 relative z-10">
                  <div className="flex gap-8 mb-3">
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Genre:</span>
                      <span className="text-gray-800 font-medium">{pitch.genre}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Type:</span>
                      <span className="text-gray-800 font-medium">{pitch.type}</span>
                    </div>
                  </div>
                  <div className="flex gap-8">
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Payout:</span>
                      <span className="text-gray-800 font-medium">{pitch.payout}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Deadline:</span>
                      <span className="text-gray-800 font-medium">{pitch.deadline}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-6 relative z-10">
                  <p className="text-gray-700 leading-relaxed text-sm">{pitch.description}</p>
                </div>

                <div className="flex justify-end relative z-10">
                  <button
                    onClick={() => handleViewDetails(pitch)}
                    className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden group"
                  >
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                    View Details
                  </button>
                </div>
              </div>
            ))
          ) : (
            // Table format for My Pitches
            <div className="bg-white/95 backdrop-blur-xl rounded-2xl border border-blue-200 shadow-blue-100 overflow-hidden opacity-0 translate-y-4" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
              {/* Table Header */}
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                  <div className="col-span-4">Publication</div>
                  <div className="col-span-2">Type</div>
                  <div className="col-span-2">Status</div>
                  <div className="col-span-2">Deadline</div>
                  <div className="col-span-2">Action</div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-100">
                {filteredPitches.map((pitch, index) => (
                  <div
                    key={pitch.id}
                    className="px-6 py-4 hover:bg-blue-50/50 transition-all duration-300 opacity-0 translate-y-4"
                    style={{
                      animationDelay: `${index * 0.1}s`,
                      animation: 'slideInUp 0.6s ease-out forwards'
                    }}
                  >
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Publication */}
                      <div className="col-span-4 flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-blue-200 flex-shrink-0">
                          <img
                            src={pitch.avatar}
                            alt="Publication"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800">{pitch.title}</h3>
                          <p className="text-sm text-blue-600">{pitch.publication}</p>
                        </div>
                      </div>

                      {/* Type */}
                      <div className="col-span-2">
                        <span className="text-gray-700">{pitch.type}</span>
                      </div>

                      {/* Status */}
                      <div className="col-span-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          pitch.status === 'Accepted'
                            ? 'bg-green-100 text-green-700'
                            : pitch.status === 'Pending'
                            ? 'bg-yellow-100 text-yellow-700'
                            : pitch.status === 'Rejected'
                            ? 'bg-red-100 text-red-700'
                            : 'bg-blue-100 text-blue-700'
                        }`}>
                          {pitch.status}
                        </span>
                      </div>

                      {/* Deadline */}
                      <div className="col-span-2">
                        <span className="text-gray-700">{pitch.deadline}</span>
                      </div>

                      {/* Action */}
                      <div className="col-span-2">
                        <button className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-0.5">
                          Action
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submission Overview */}
      {showSubmissionOverview && selectedPitch && !showPitchForm && (
        <div ref={submissionOverviewRef} className="max-w-5xl mx-auto opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          {/* Back to Pitches Button */}
          <div className="mb-8">
            <button
              onClick={handleBackFromOverview}
              className="group flex items-center gap-3 px-6 py-3 bg-white/80 hover:bg-white backdrop-blur-sm rounded-xl transition-all duration-300 text-gray-700 hover:text-blue-600 shadow-lg hover:shadow-xl hover:-translate-y-1 border border-gray-200 hover:border-blue-200"
            >
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white flex items-center justify-center group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                  <path d="M19 12H5M12 19l-7-7 7-7"/>
                </svg>
              </div>
              <span className="font-semibold">Back to Pitches</span>
            </button>
          </div>
          {/* Submission Overview Content */}
          <div className="bg-white rounded-3xl shadow-2xl border border-blue-100 overflow-hidden relative">
            {/* Decorative Background Elements */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-indigo-500/10 via-blue-500/5 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute top-1/4 left-1/3 w-48 h-48 bg-gradient-to-tr from-pink-500/5 to-transparent rounded-full blur-xl"></div>
            
            {/* Header */}
            <div className="relative p-8 bg-gradient-to-r from-blue-50/90 via-indigo-50/80 to-purple-50/70 border-b border-blue-200/50 shadow-sm">
              <div className="flex items-center gap-4 mb-3">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg ring-4 ring-blue-100">
                  <svg className="w-6 h-6 text-white drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                    Submission Overview
                  </h1>
                  <p className="text-gray-500 text-sm mt-1">Review the details and prepare your pitch</p>
                </div>
              </div>
              <div className="w-full h-1 bg-gradient-to-r from-blue-300 via-indigo-300 to-purple-300 rounded-full mt-2"></div>
            </div>

            {/* Content */}
            <div className="relative p-8">
              {/* Description Section */}
              <div className="mb-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-gray-600 to-slate-700 rounded-lg flex items-center justify-center shadow-md">
                    <svg className="w-4 h-4 text-white drop-shadow-sm" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold bg-gradient-to-r from-gray-700 to-slate-800 bg-clip-text text-transparent">Description</h2>
                </div>
                <div className="bg-gradient-to-r from-gray-50/90 to-slate-50/80 rounded-xl p-6 border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <p className="text-gray-700 leading-relaxed mb-5 text-sm">
                    Looking for compelling short Horror stories with strong character development and unexpected twists. 
                    Preferred themes: mystery, adventure, and psychological drama. Word count: 2,500 - 3,000 words. 
                    Submissions should be original and unpublished
                  </p>
                  
                  {/* Info Grid */}
                  <div className="grid grid-cols-2 gap-6 mt-6">
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow transition-shadow duration-300">
                        <span className="font-semibold text-gray-700">Genre:</span>
                        <span className="px-3 py-1.5 bg-blue-100 text-blue-700 rounded-md text-sm font-medium shadow-inner">{selectedPitch.genre}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow transition-shadow duration-300">
                        <span className="font-semibold text-gray-700">Payout:</span>
                        <span className="px-3 py-1.5 bg-green-100 text-green-700 rounded-md text-sm font-bold shadow-inner">{selectedPitch.payout}</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center p-3 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow transition-shadow duration-300">
                        <span className="font-semibold text-gray-700">Content Type:</span>
                        <span className="px-3 py-1.5 bg-purple-100 text-purple-700 rounded-md text-sm font-medium shadow-inner">{selectedPitch.type}</span>
                      </div>
                      <div className="flex justify-between items-center p-3 bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow transition-shadow duration-300">
                        <span className="font-semibold text-gray-700">Deadline:</span>
                        <span className="px-3 py-1.5 bg-red-100 text-red-700 rounded-md text-sm font-medium shadow-inner">{selectedPitch.deadline}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full h-0.5 bg-gradient-to-r from-gray-200 via-blue-200 to-gray-200 rounded-full mb-8"></div>

              {/* Payout Section */}
              <div className="mb-8">
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-md ring-4 ring-green-100">
                    <svg className="w-5 h-5 text-white drop-shadow-md" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                    Payout
                  </h2>
                </div>
                <div className="bg-gradient-to-br from-green-50/90 to-emerald-50/80 rounded-xl p-6 border border-green-200 shadow-md hover:shadow-lg transition-shadow duration-300">
                  <div className="space-y-4">
                    {/* Your Quote */}
                    <div className="flex items-center justify-between">
                      <label className="text-base font-semibold text-gray-700 flex items-center gap-2">
                        <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                        Your Quote:
                      </label>
                      <input
                        type="number"
                        value={pitchFormData.quote}
                        onChange={(e) => handlePitchFormChange('quote', parseInt(e.target.value) || 0)}
                        className="w-48 px-4 py-3 border-2 border-blue-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 text-base font-semibold shadow-sm transition-all duration-200"
                        placeholder="Rs. 1000"
                      />
                    </div>

                    {/* Service Fee */}
                    <div className="flex items-center justify-between">
                      <label className="text-base font-semibold text-gray-700 flex items-center gap-2">
                        <div className="w-4 h-4 bg-orange-500 rounded-full"></div>
                        5% Service Fee:
                      </label>
                      <div className="w-48 px-4 py-3 bg-orange-50 border-2 border-orange-200 rounded-xl text-base font-semibold text-orange-700 shadow-sm">
                        Rs. {Math.round(pitchFormData.quote * 0.05)}
                      </div>
                    </div>

                    {/* You'll Get */}
                    <div className="flex items-center justify-between p-4 bg-white/80 rounded-xl border-2 border-green-200 shadow-md">
                      <label className="text-lg font-bold text-gray-800 flex items-center gap-2">
                        <div className="w-5 h-5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center">
                          <svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                        You'll Get:
                      </label>
                      <div className="w-48 px-4 py-3 bg-gradient-to-r from-green-100 to-emerald-100 border-2 border-green-300 rounded-xl text-lg font-bold text-green-700 shadow-md">
                        Rs. {pitchFormData.quote - Math.round(pitchFormData.quote * 0.05)}
                      </div>
                    </div>

                    <div className="flex items-center justify-center mt-4">
                      <p className="text-red-600 text-xs font-semibold bg-red-50 px-3 py-1 rounded-full border border-red-200">
                        The final amount credited to you after service fees.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-full h-0.5 bg-gradient-to-r from-gray-200 via-blue-200 to-gray-200 rounded-full mb-8"></div>

              {/* Submit your Pitch Section */}
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-6 h-6 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </div>
                  <h2 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                    Submit your Pitch:
                  </h2>
                </div>
                
                {/* Title */}
                <div className="mb-5">
                  <label className="block text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    Title
                  </label>
                  <input
                    type="text"
                    value={pitchFormData.title}
                    onChange={(e) => handlePitchFormChange('title', e.target.value)}
                    placeholder="Enter Title"
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 text-gray-700 shadow-sm transition-all duration-200 hover:border-gray-300"
                  />
                </div>

                {/* Cover Image */}
                <div className="mb-5">
                  <label className="block text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <div className="w-4 h-4 bg-purple-500 rounded-full"></div>
                    Cover Image
                  </label>
                  <div className="border-2 border-dashed border-purple-300 rounded-xl p-6 text-center hover:border-purple-400 transition-all duration-300 bg-gradient-to-br from-purple-50/50 to-pink-50/50 hover:from-purple-50 hover:to-pink-50 shadow-sm">
                    <input
                      name="coverImage"
                      type="file"
                      accept="image/*"
                      onChange={handleFileChange}
                      className="hidden"
                      id="cover-upload"
                    />
                    <label htmlFor="cover-upload" className="cursor-pointer">
                      {previewImage ? (
                        <div className="space-y-4">
                          <div className="relative mx-auto w-32 h-32">
                            <img src={previewImage} alt="Preview" className="w-full h-full object-cover rounded-xl shadow-lg" />
                            <div className="absolute inset-0 bg-black/20 rounded-xl flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300">
                              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                            </div>
                          </div>
                          <p className="text-sm text-purple-600 font-semibold">Click to change image</p>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div className="mx-auto w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                          </div>
                          <div>
                            <p className="text-lg font-bold text-purple-700">Add a cover image</p>
                            <p className="text-sm text-purple-600 mt-1">JPEG or PNG, max 5MB</p>
                          </div>
                        </div>
                      )}
                    </label>
                  </div>
                </div>

                {/* Draft Section */}
                <div className="mb-8">
                  <label className="block text-base font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <div className="w-4 h-4 bg-indigo-500 rounded-full"></div>
                    Draft
                  </label>

                  {/* Toolbar */}
                  <div className="border-2 border-gray-200 rounded-t-xl bg-gradient-to-r from-gray-50 to-slate-50 p-3 flex items-center gap-2 flex-wrap shadow-sm">
                    <select className="px-3 py-1.5 border border-gray-300 rounded-lg text-xs bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <option>16</option>
                      <option>14</option>
                      <option>18</option>
                      <option>20</option>
                    </select>
                    <div className="w-px h-6 bg-gray-300 mx-1"></div>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg font-bold text-sm transition-all duration-200">B</button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg italic text-sm transition-all duration-200">I</button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg underline text-sm transition-all duration-200">U</button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg line-through text-sm transition-all duration-200">S</button>
                    <div className="w-px h-6 bg-gray-300 mx-1"></div>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                    </button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h8M4 18h16" />
                      </svg>
                    </button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h4M4 18h16" />
                      </svg>
                    </button>
                    <div className="w-px h-6 bg-gray-300 mx-1"></div>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200 text-sm">•</button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200 text-xs">1.</button>
                    <div className="w-px h-6 bg-gray-300 mx-1"></div>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                      </svg>
                    </button>
                    <button className="p-2 hover:bg-white hover:shadow-sm rounded-lg transition-all duration-200">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                      </svg>
                    </button>
                    <div className="ml-auto">
                      <button className="px-4 py-1.5 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 rounded-lg text-xs font-semibold hover:from-blue-200 hover:to-indigo-200 transition-all duration-200 shadow-sm">Tags</button>
                    </div>
                  </div>

                  {/* Text Editor */}
                  <textarea
                    value={pitchFormData.content}
                    onChange={(e) => handlePitchFormChange('content', e.target.value)}
                    placeholder="Start typing here..."
                    className="w-full h-64 p-4 border-2 border-gray-200 border-t-0 rounded-b-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-400 resize-none text-gray-700 leading-relaxed text-sm shadow-sm transition-all duration-200"
                  />

                  {/* Stats */}
                  <div className="mt-3 text-xs text-gray-500 flex gap-6 justify-end bg-gray-50 px-4 py-2 rounded-lg">
                    {(() => {
                      const stats = getTextStats(pitchFormData.content);
                      return (
                        <>
                          <span className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                            Paragraphs: {stats.paragraphs}
                          </span>
                          <span className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            Words: {stats.words}
                          </span>
                          <span className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                            Characters: {stats.characters}
                          </span>
                        </>
                      );
                    })()} 
                  </div>
                </div>

                {/* Submission Fee Notice */}
                <div className="mb-8 p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl shadow-sm">
                  <div className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center mt-0.5">
                      <svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <div className="space-y-2">
                      <p className="text-red-700 text-sm font-bold">
                        ** Submission Fee: Rs. 25 Per Submission (Non-Refundable).
                      </p>
                      <p className="text-red-600 text-sm font-semibold">
                        ** This Fee Helps Maintain The Quality Of Submissions And Supports Publications In Bringing More Opportunities For Authors.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-4 justify-center">
                  <button className="group px-8 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl font-semibold hover:from-gray-600 hover:to-gray-700 transition-all duration-300 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      Save
                    </span>
                  </button>
                  <button className="group px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      Preview
                    </span>
                  </button>
                  <button
                    onClick={handlePitchSubmit}
                    className="group px-8 py-3 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl font-semibold hover:from-green-600 hover:to-emerald-700 transition-all duration-300 text-sm shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                  >
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      Submit
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detail View */}
      {showDetailView && selectedPitch && !showPitchForm && !showSubmissionOverview && !showSuccessPage && (
        <div className="opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          {/* Back Button */}
          <div className="mb-8">
            <button
              onClick={handleBackToList}
              className="group flex items-center gap-3 px-6 py-3 bg-white/80 hover:bg-white backdrop-blur-sm rounded-xl transition-all duration-300 text-gray-700 hover:text-blue-600 shadow-lg hover:shadow-xl hover:-translate-y-1 border border-gray-200 hover:border-blue-200"
            >
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white flex items-center justify-center group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                <path d="M19 12H5M12 19l-7-7 7-7"/>
              </svg>
              </div>
              <span className="font-semibold">Back to Pitches</span>
            </button>
          </div>

          {/* Detail Content */}
          <div className="bg-white/95 backdrop-blur-2xl rounded-3xl shadow-2xl border border-blue-200/50 overflow-hidden relative">
            {/* Decorative Background Elements */}
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-80 h-80 bg-gradient-to-tr from-indigo-500/10 via-blue-500/5 to-transparent rounded-full blur-3xl"></div>

            {/* Header */}
            <div className="relative p-8 border-b border-blue-100/50 bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 backdrop-blur-sm">
              <div className="flex items-start gap-8">
                <div className="relative group">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
                  <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-2xl group-hover:scale-105 transition-transform duration-300">
                  <img
                    src={selectedPitch.avatar}
                    alt={selectedPitch.publication}
                    className="w-full h-full object-cover"
                  />
                  </div>
                  <div className="absolute -bottom-1 -right-1 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="mb-3">
                    <span className="inline-block px-4 py-1 bg-blue-600 text-white text-sm font-semibold rounded-full shadow-md">
                      Requested By: {selectedPitch.publication}
                    </span>
                  </div>
                  <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-800 via-blue-700 to-purple-700 bg-clip-text text-transparent mb-4 leading-tight">
                    {selectedPitch.title}
                  </h1>
                  <div className="flex items-center gap-2 text-gray-600">
                    <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-semibold">Deadline: {selectedPitch.deadline}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="relative p-8">
              {/* Quick Info Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10">
                <div className="group relative overflow-hidden bg-gradient-to-br from-blue-50 via-blue-100/50 to-indigo-100 rounded-2xl p-5 border border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-blue-500/20 to-transparent rounded-full blur-xl"></div>
                  <div className="relative">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                  </div>
                      <span className="text-sm font-semibold text-blue-600 uppercase tracking-wider">Genre & Payout</span>
                  </div>
                    <div className="space-y-2">
                      <div className="text-xl font-bold text-gray-800">{selectedPitch.genre}</div>
                      <div className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                        {selectedPitch.payout}
                </div>
                  </div>
                  </div>
                </div>
                
                <div className="group relative overflow-hidden bg-gradient-to-br from-purple-50 via-purple-100/50 to-pink-100 rounded-2xl p-5 border border-purple-200/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-purple-500/20 to-transparent rounded-full blur-xl"></div>
                  <div className="relative">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <span className="text-sm font-semibold text-purple-600 uppercase tracking-wider">Type & Deadline</span>
                    </div>
                    <div className="space-y-2">
                      <div className="text-xl font-bold text-gray-800">{selectedPitch.type}</div>
                      <div className="text-base font-bold text-gray-700">{selectedPitch.deadline}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description Section */}
              <div className="relative mb-10 group">
                <div className="absolute inset-0 bg-gradient-to-r from-gray-100/50 to-slate-100/50 rounded-3xl blur-xl opacity-50"></div>
                <div className="relative bg-gradient-to-br from-gray-50/80 via-slate-50/60 to-gray-100/40 backdrop-blur-sm rounded-3xl p-10 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center gap-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-gray-600 to-slate-700 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                    </div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-slate-700 bg-clip-text text-transparent">
                  Description
                </h2>
                  </div>
                  <div className="bg-white/70 rounded-2xl p-8 shadow-inner border border-gray-200/50">
                    <p className="text-gray-700 leading-relaxed text-base font-medium">{selectedPitch.description}</p>
                  </div>
                </div>
              </div>

              {/* Submission Guidelines */}
              <div className="relative mb-10 group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-indigo-100/50 rounded-3xl blur-xl opacity-50"></div>
                <div className="relative bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-blue-100/40 backdrop-blur-sm rounded-3xl p-10 border border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center gap-4 mb-8">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                  </svg>
                    </div>
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-700 to-indigo-700 bg-clip-text text-transparent">
                  Submission Guidelines
                </h2>
                  </div>
                  <div className="bg-white/70 rounded-2xl p-8 shadow-inner border border-blue-200/50">
                    <p className="text-blue-800 font-semibold mb-6 text-lg">Below are the submission requirements set by the publication.</p>
                    <p className="text-gray-700 mb-8 leading-relaxed">Please review the guidelines carefully before submitting your draft to ensure it aligns with their needs.</p>
                    <div className="space-y-5">
                      {[
                        "Original work only (unpublished anywhere else)",
                        "Submit before: June 30, 2025",
                        "Word Count: 800–1200",
                        "Format: Text only",
                        "First Publication Rights Required"
                      ].map((guideline, index) => (
                        <div key={index} className="flex items-start gap-4 group/item">
                          <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mt-0.5 shadow-md group-hover/item:scale-110 transition-transform duration-300">
                            <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                            </svg>
                  </div>
                          <p className="text-gray-700 font-medium text-lg leading-relaxed group-hover/item:text-gray-800 transition-colors duration-300">
                            {index === 4 ? (
                              <>
                                <span className="text-blue-600 underline cursor-pointer hover:text-blue-700">
                                  First Publication Rights Required
                                </span>
                                <br />
                                <span className="text-sm text-gray-600 mt-1 block">
                                  (Your work must not be published elsewhere before this. Republishing is typically allowed later.)
                                </span>
                              </>
                            ) : (
                              guideline
                            )}
                          </p>
                  </div>
                      ))}
                  </div>
                  </div>
                </div>
              </div>

              {/* Ready to Submit Section */}
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-200 to-blue-400 rounded-3xl opacity-50"></div>
                <div className="relative bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 rounded-3xl p-8 text-center text-white shadow-2xl hover:shadow-blue-300/40 transition-all duration-300 hover:-translate-y-1 overflow-hidden border border-white/30">
                  <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/30 to-transparent"></div>
                  
                  <div className="relative z-10">
                    <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg border-4 border-blue-200">
                      <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                    </div>
                    <h2 className="text-3xl font-bold mb-4 drop-shadow-md">Ready to Submit?</h2>
                    <p className="text-white mb-8 leading-relaxed text-lg max-w-2xl mx-auto font-medium drop-shadow">
                  Once you've reviewed the guidelines and ensured your draft meets the requirements, click the button below to submit your content.
                </p>
                <button
                  onClick={handlePitchClick}
                      className="group/btn relative inline-flex items-center gap-3 bg-white text-blue-600 px-10 py-4 rounded-2xl font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-2xl shadow-xl overflow-hidden border-2 border-white"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-100/50 to-transparent -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>
                      <svg className="w-6 h-6 group-hover/btn:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                      </svg>
                      <span className="relative z-10 font-extrabold">Pitch</span>
                </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Pitch Submission Form */}
      {/* ...rest of the code remains unchanged... */}
      {/* (No changes needed for Pitch Submission Form and below) */}
      {/* ... */}
    </div>
  );
};

export default AuthorPitch;