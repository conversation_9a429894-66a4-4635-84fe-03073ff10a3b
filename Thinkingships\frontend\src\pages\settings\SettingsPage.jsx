import { useState } from 'react';
import { FaShieldAlt } from 'react-icons/fa';

function SettingsPage() {
  const [activeTab, setActiveTab] = useState('account');
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
    delete: false
  });

  const togglePasswordVisibility = (field) => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <div className="max-w-6xl mx-auto px-6 py-10">
        {/* Settings Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg">
            <FaShieldAlt className="text-white text-2xl" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Settings</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Manage your account preferences, notifications, and privacy settings all in one place.
            Customize your Skrivee experience to suit your needs.
          </p>
        </div>

        {/* Settings Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
          {/* Account Card */}
          <div
            onClick={() => setActiveTab('account')}
            className={`group relative p-4 rounded-xl cursor-pointer transition-all duration-200 h-36 ${
              activeTab === 'account'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10 h-full flex flex-col">
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg mb-2 transition-all duration-200 ${
                activeTab === 'account'
                  ? 'bg-white/20 text-white'
                  : 'bg-blue-50 text-blue-600 group-hover:bg-blue-100'
              }`}>
                <FaShieldAlt className="text-base" />
              </div>
              <h3 className="text-sm font-semibold mb-2">Account</h3>
              <p className={`text-xs leading-tight flex-1 ${activeTab === 'account' ? 'text-blue-100' : 'text-gray-600'}`}>
                Manage your account details, change your email or password and delete your account if needed.
              </p>
            </div>
          </div>

          {/* Notifications Card */}
          <div
            onClick={() => setActiveTab('notifications')}
            className={`group relative p-4 rounded-xl cursor-pointer transition-all duration-200 h-36 ${
              activeTab === 'notifications'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10 h-full flex flex-col">
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg mb-2 transition-all duration-200 ${
                activeTab === 'notifications'
                  ? 'bg-white/20 text-white'
                  : 'bg-amber-50 text-amber-600 group-hover:bg-amber-100'
              }`}>
                <FaShieldAlt className="text-base" />
              </div>
              <h3 className="text-sm font-semibold mb-2">Notifications</h3>
              <p className={`text-xs leading-tight flex-1 ${activeTab === 'notifications' ? 'text-blue-100' : 'text-gray-600'}`}>
                Customize your notification preferences for messages, content engagement and other updates.
              </p>
            </div>
          </div>

          {/* Payment & Subscription Card */}
          <div
            onClick={() => setActiveTab('payment')}
            className={`group relative p-4 rounded-xl cursor-pointer transition-all duration-200 h-36 ${
              activeTab === 'payment'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10 h-full flex flex-col">
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg mb-2 transition-all duration-200 ${
                activeTab === 'payment'
                  ? 'bg-white/20 text-white'
                  : 'bg-green-50 text-green-600 group-hover:bg-green-100'
              }`}>
                <FaShieldAlt className="text-base" />
              </div>
              <h3 className="text-sm font-semibold mb-2">Payment & Subscription</h3>
              <p className={`text-xs leading-tight flex-1 ${activeTab === 'payment' ? 'text-blue-100' : 'text-gray-600'}`}>
                View and manage your subscription plan and payment methods.
              </p>
            </div>
          </div>

          {/* Feedback and Support Card */}
          <div
            onClick={() => setActiveTab('support')}
            className={`group relative p-4 rounded-xl cursor-pointer transition-all duration-200 h-36 ${
              activeTab === 'support'
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : 'bg-white text-gray-700 border border-gray-200 hover:border-blue-300 hover:shadow-lg hover:shadow-gray-200/50'
            }`}
          >
            <div className="relative z-10 h-full flex flex-col">
              <div className={`inline-flex items-center justify-center w-10 h-10 rounded-lg mb-2 transition-all duration-200 ${
                activeTab === 'support'
                  ? 'bg-white/20 text-white'
                  : 'bg-purple-50 text-purple-600 group-hover:bg-purple-100'
              }`}>
                <FaShieldAlt className="text-base" />
              </div>
              <h3 className="text-sm font-semibold mb-2">Feedback and Support</h3>
              <p className={`text-xs leading-tight flex-1 ${activeTab === 'support' ? 'text-blue-100' : 'text-gray-600'}`}>
                Report issues, contact support and access helpful resources.
              </p>
            </div>
          </div>
        </div>

        {/* Account Settings Section */}
        {activeTab === 'account' && (
          <div className="animate-fadeIn">
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl mb-6 shadow-lg">
                <FaShieldAlt className="text-white text-2xl" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Account Settings</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Manage your personal information, security settings, and account preferences to keep your profile secure and up-to-date.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Account Details */}
              <div className="space-y-8">
                {/* Email Update */}
                <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-2xl font-bold text-gray-900">Email Address</h4>
                      <p className="text-sm text-gray-600">Update your primary email address</p>
                    </div>
                    <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                      Update
                    </button>
                  </div>
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                    <input
                      type="email"
                      defaultValue="<EMAIL>"
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-white text-gray-900 font-medium"
                    />
                  </div>
                </div>

                {/* Current Password */}
                <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-2xl font-bold text-gray-900">Current Password</h4>
                      <p className="text-sm text-gray-600">Change your account password for security</p>
                    </div>
                    <button className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                      Update
                    </button>
                  </div>
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-4 border border-green-100">
                    <div className="relative">
                      <input
                        type={showPassword.current ? "text" : "password"}
                        defaultValue="••••••••••••••••••"
                        className="w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all bg-white text-gray-900 font-medium"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('current')}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-green-600 transition-colors p-2 rounded-lg hover:bg-green-100"
                      >
                        {showPassword.current ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>
                </div>

                {/* New Password */}
                <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl flex items-center justify-center">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-2xl font-bold text-gray-900">New Password</h4>
                      <p className="text-sm text-gray-600">Create a strong and secure password</p>
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl p-4 border border-purple-100">
                    <div className="relative">
                      <input
                        type={showPassword.new ? "text" : "password"}
                        placeholder="••••••••••••••••••"
                        className="w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all bg-white text-gray-900 font-medium"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('new')}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-colors p-2 rounded-lg hover:bg-purple-100"
                      >
                        {showPassword.new ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Confirm New Password */}
                <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl flex items-center justify-center">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div className="flex-1">
                      <h4 className="text-2xl font-bold text-gray-900">Confirm New Password</h4>
                      <p className="text-sm text-gray-600">Re-enter your new password to confirm</p>
                    </div>
                    <button className="bg-gradient-to-r from-amber-600 to-orange-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-amber-700 hover:to-orange-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                      Save
                    </button>
                  </div>
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-4 border border-amber-100">
                    <div className="relative">
                      <input
                        type={showPassword.confirm ? "text" : "password"}
                        placeholder="••••••••••••••••••"
                        className="w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all bg-white text-gray-900 font-medium"
                      />
                      <button
                        type="button"
                        onClick={() => togglePasswordVisibility('confirm')}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-amber-600 transition-colors p-2 rounded-lg hover:bg-amber-100"
                      >
                        {showPassword.confirm ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column - Delete Account */}
              <div>
                <div className="bg-white rounded-2xl p-8 shadow-xl border border-red-100 hover:shadow-2xl transition-all duration-300">
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-red-700">Delete Account</h3>
                      <p className="text-sm text-gray-600">Permanently remove your account</p>
                    </div>
                  </div>

                  <div className="space-y-8 mb-8">
                    <div className="bg-gradient-to-r from-red-50 to-rose-50 rounded-xl p-4 border border-red-100">
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Email Address*</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all bg-white text-gray-900 font-medium"
                      />
                    </div>

                    <div className="bg-gradient-to-r from-red-50 to-rose-50 rounded-xl p-4 border border-red-100">
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Current Password*</label>
                      <div className="relative">
                        <input
                          type={showPassword.delete ? "text" : "password"}
                          placeholder="••••••••••••••••••"
                          className="w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all bg-white text-gray-900 font-medium"
                        />
                        <button
                          type="button"
                          onClick={() => togglePasswordVisibility('delete')}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-red-600 transition-colors p-2 rounded-lg hover:bg-red-100"
                        >
                          {showPassword.delete ? <FaShieldAlt className="w-5 h-5" /> : <FaShieldAlt className="w-5 h-5" />}
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="mb-8 p-6 bg-gradient-to-r from-red-50 to-rose-50 border-2 border-red-200 rounded-xl">
                    <div className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center flex-shrink-0">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-red-700 mb-2">Warning!</h4>
                        <p className="text-sm text-red-700 font-medium leading-relaxed">
                          This Action Cannot Be Undone. All Your Data Will Be Permanently Deleted.
                        </p>
                      </div>
                    </div>
                  </div>

                  <button className="w-full bg-gradient-to-r from-red-600 to-rose-600 text-white py-4 rounded-xl font-bold text-lg hover:from-red-700 hover:to-rose-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    Delete Account
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Notifications Settings */}
        {activeTab === 'notifications' && (
          <div className="animate-fadeIn">
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl mb-6 shadow-lg">
                <FaShieldAlt className="text-white text-2xl" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Notification Preferences</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Customize how and when you receive notifications to stay connected without being overwhelmed.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Push Notifications */}
              <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                    <FaShieldAlt className="text-white text-lg" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Push Notifications</h3>
                </div>

                <div className="space-y-8">
                  {/* Likes */}
                  <div className="bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl p-6 border border-pink-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Likes</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="likes_push" className="w-5 h-5 text-pink-600 focus:ring-pink-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="likes_push" className="w-5 h-5 text-pink-600 focus:ring-pink-500" />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="likes_push" className="w-5 h-5 text-pink-600 focus:ring-pink-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">From Everyone</span>
                      </label>
                    </div>
                  </div>

                  {/* Comments */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Comments</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="comments_push" className="w-5 h-5 text-blue-600 focus:ring-blue-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="comments_push" className="w-5 h-5 text-blue-600 focus:ring-blue-500" />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="comments_push" className="w-5 h-5 text-blue-600 focus:ring-blue-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">From Everyone</span>
                      </label>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Messages</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="messages_push" className="w-5 h-5 text-green-600 focus:ring-green-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="messages_push" className="w-5 h-5 text-green-600 focus:ring-green-500" />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="messages_push" className="w-5 h-5 text-green-600 focus:ring-green-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">From Everyone</span>
                      </label>
                    </div>
                  </div>

                  {/* Skrivee */}
                  <div className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Skrivee</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="skrivee_push" className="w-5 h-5 text-purple-600 focus:ring-purple-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="skrivee_push" className="w-5 h-5 text-purple-600 focus:ring-purple-500" />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="skrivee_push" className="w-5 h-5 text-purple-600 focus:ring-purple-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">From Everyone</span>
                      </label>
                    </div>
                  </div>

                  {/* Birthdays Reminders */}
                  <div className="bg-gradient-to-r from-yellow-50 to-amber-50 rounded-xl p-6 border border-yellow-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Birthday Reminders</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="birthdays_push" className="w-5 h-5 text-yellow-600 focus:ring-yellow-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="birthdays_push" className="w-5 h-5 text-yellow-600 focus:ring-yellow-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">On</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Email Notifications */}
              <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-10 h-10 bg-gradient-to-br from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
                    <FaShieldAlt className="text-white text-lg" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Email Notifications</h3>
                </div>

                <div className="space-y-8">
                  {/* Feedback emails */}
                  <div className="bg-gradient-to-r from-teal-50 to-cyan-50 rounded-xl p-6 border border-teal-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-teal-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Feedback Emails</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="feedback_email" className="w-5 h-5 text-teal-600 focus:ring-teal-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="feedback_email" className="w-5 h-5 text-teal-600 focus:ring-teal-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">On</span>
                      </label>
                    </div>
                  </div>

                  {/* Reminder emails */}
                  <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Reminder Emails</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="reminder_email" className="w-5 h-5 text-orange-600 focus:ring-orange-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="reminder_email" className="w-5 h-5 text-orange-600 focus:ring-orange-500" />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                    </div>
                  </div>

                  {/* Support emails */}
                  <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border border-indigo-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Support Emails</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="support_email" className="w-5 h-5 text-indigo-600 focus:ring-indigo-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="support_email" className="w-5 h-5 text-indigo-600 focus:ring-indigo-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                    </div>
                  </div>

                  {/* Product emails */}
                  <div className="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-xl p-6 border border-emerald-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Product Emails</h4>
                    </div>
                    <div className="space-y-3">
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer">
                        <input type="radio" name="product_email" className="w-5 h-5 text-emerald-600 focus:ring-emerald-500" />
                        <span className="text-sm font-medium text-gray-700">Off</span>
                      </label>
                      <label className="flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors cursor-pointer bg-white/70">
                        <input type="radio" name="product_email" className="w-5 h-5 text-emerald-600 focus:ring-emerald-500" defaultChecked />
                        <span className="text-sm font-medium text-gray-700">From Faves</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payment' && (
          <div className="animate-fadeIn">
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-600 to-emerald-600 rounded-2xl mb-6 shadow-lg">
                <FaShieldAlt className="text-white text-2xl" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Payment & Subscription</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Manage your subscription plans, billing information, and payment methods all in one secure location.
              </p>
            </div>

            {/* Coming Soon Section */}
            <div className="bg-white rounded-2xl p-16 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
              <div className="text-center">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 rounded-3xl mb-8 shadow-lg">
                  <FaShieldAlt className="text-white text-3xl" />
                </div>

                <h3 className="text-4xl font-bold text-gray-900 mb-6">Coming Soon</h3>

                <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                  We're working hard to bring you an amazing payment and subscription experience.
                  Stay tuned for exciting features and flexible pricing plans!
                </p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                    <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">Flexible Plans</h4>
                    <p className="text-sm text-gray-600">Choose from multiple subscription options that fit your needs</p>
                  </div>

                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
                    <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">Secure Payments</h4>
                    <p className="text-sm text-gray-600">Safe and secure payment processing with multiple payment methods</p>
                  </div>

                  <div className="bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl p-6 border border-purple-100">
                    <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <FaShieldAlt className="text-white text-lg" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">Premium Features</h4>
                    <p className="text-sm text-gray-600">Unlock advanced features and exclusive content with premium plans</p>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-xl p-6 border border-gray-200">
                  <p className="text-lg font-semibold text-gray-700 mb-2">Get Notified When We Launch</p>
                  <p className="text-sm text-gray-600">Be the first to know when our payment and subscription features go live!</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'support' && (
          <div className="animate-fadeIn">
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-2xl mb-6 shadow-lg">
                <FaShieldAlt className="text-white text-2xl" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Feedback and Support</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                Help us improve Skrivee by sharing your thoughts or reporting issues. We're here to help you succeed.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Feedback Section */}
              <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <FaShieldAlt className="text-white text-lg" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Share Your Feedback</h3>
                </div>

                <div className="space-y-6">
                  {/* Your Name */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Your Name*</label>
                    <input
                      type="text"
                      placeholder="Enter your full name"
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white group-hover:border-gray-300 text-gray-900 placeholder-gray-500"
                    />
                  </div>

                  {/* Email Address */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Email Address*</label>
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white group-hover:border-gray-300 text-gray-900 placeholder-gray-500"
                    />
                  </div>

                  {/* Subject */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Subject*</label>
                    <input
                      type="text"
                      placeholder="Brief description of your feedback"
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white group-hover:border-gray-300 text-gray-900 placeholder-gray-500"
                    />
                  </div>

                  {/* Message */}
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3">Message*</label>
                    <textarea
                      rows="5"
                      placeholder="Tell us about your experience, suggestions, or any issues you've encountered..."
                      className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-gray-50 focus:bg-white group-hover:border-gray-300 text-gray-900 placeholder-gray-500 resize-none"
                    ></textarea>
                  </div>

                  <button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                    Send Feedback
                  </button>
                </div>
              </div>

              {/* Support & Help Section */}
              <div className="bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300">
                <div className="flex items-center gap-3 mb-8">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center">
                    <FaShieldAlt className="text-white text-lg" />
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900">Support & Help</h3>
                </div>

                <div className="space-y-8">
                  {/* Help Section */}
                  <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Help Center</h4>
                    </div>
                    <p className="text-gray-700 mb-4">Having trouble logging in? Here's a comprehensive guide to help you get started!</p>
                    <a href="#" className="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                      <FaShieldAlt className="w-4 h-4" />
                      View Help Guide
                    </a>
                  </div>

                  {/* Support Tickets */}
                  <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-6 border border-amber-100">
                    <div className="flex items-center justify-between">
                      <p className="text-gray-700">No active support tickets found.</p>
                      <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">All Clear</span>
                    </div>
                  </div>

                  {/* Reported Issues */}
                  <div className="bg-gradient-to-r from-red-50 to-pink-50 rounded-xl p-6 border border-red-100">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
                        <FaShieldAlt className="text-white text-sm" />
                      </div>
                      <h4 className="text-lg font-bold text-gray-800">Report an Issue</h4>
                    </div>

                    <div className="mb-6">
                      <label className="block text-sm font-semibold text-gray-700 mb-3">Describe the issue you're experiencing*</label>
                      <textarea
                        rows="4"
                        placeholder="Please provide detailed information about the issue, including steps to reproduce it..."
                        className="w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all bg-white text-gray-900 placeholder-gray-500 resize-none"
                      ></textarea>
                    </div>

                    <button className="bg-gradient-to-r from-red-600 to-pink-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-red-700 hover:to-pink-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                      Submit Report
                    </button>
                  </div>

                  {/* Contact Support */}
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-100 text-center">
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                      <FaShieldAlt className="text-white text-xl" />
                    </div>
                    <h4 className="text-lg font-bold text-gray-800 mb-2">Need Direct Support?</h4>
                    <p className="text-gray-700 mb-4">
                      Our support team is here to help you with any questions or concerns.
                    </p>
                    <a href="mailto:<EMAIL>" className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl">
                      <FaShieldAlt className="w-4 h-4" />
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Custom CSS for animations */}
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.6s ease-out forwards;
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.8;
          }
        }

        .animate-pulse {
          animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>
    </div>
  );
}

export default SettingsPage;