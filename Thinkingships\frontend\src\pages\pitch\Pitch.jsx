import { useState } from 'react';

const Pitch = () => {
  const [activeTab, setActiveTab] = useState('Open');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showDetailsView, setShowDetailsView] = useState(false);
  const [showStoryView, setShowStoryView] = useState(false);
  const [selectedPitch, setSelectedPitch] = useState(null);
  const [selectedSubmission, setSelectedSubmission] = useState(null);
  const [filters, setFilters] = useState({
    contentType: 'All',
    genre: 'All',
    language: 'All',
    payout: 'All'
  });
  const [formData, setFormData] = useState({
    title: '',
    brief: '',
    contentType: '',
    genre: '',
    deadline: '',
    wordCount: '',
    language: '',
    tags: '',
    guidelines: '',
    requireFirstRights: false,
    showSubmissionCount: false,
    autoCloseDeadline: false
  });

  const openPitchRequests = [
    {
      id: 1,
      title: 'Pitch Request By Inkspire Magazine',
      publication: 'Voices From The City',
      genre: 'Urban Life',
      type: 'Blog',
      payout: '₹500',
      submissions: 500,
      status: 'Open',
      description: 'Share A Compelling Article About Life In An Indian Metro City — The Chaos, The Quiet Moments, The Everyday Characters, And The Ever-Changing Pace.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Pitch Request By Inkspire Magazine',
      publication: 'Echoes Of The Forgotten',
      genre: 'Romance',
      type: 'Story',
      payout: '₹500',
      submissions: 500,
      status: 'Open',
      description: 'A Haunting Narrative About People, Places, Or Memories That Time Has Left Behind. We\'re Looking For Stories That Blend Nostalgia With Quiet Revelation.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Pitch Request By Literary Digest',
      publication: 'Modern Myths',
      genre: 'Fantasy',
      type: 'Story',
      payout: '₹750',
      submissions: 320,
      status: 'Open',
      description: 'Contemporary retellings of ancient myths and folklore. We want fresh perspectives on timeless stories that resonate with modern audiences.',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    },
    {
      id: 4,
      title: 'Pitch Request By Creative Writers Hub',
      publication: 'Tech Tomorrow',
      genre: 'Science Fiction',
      type: 'Article',
      payout: '₹600',
      submissions: 150,
      status: 'Open',
      description: 'Explore the intersection of technology and human emotion. How will AI, robotics, and digital transformation shape our relationships and society?',
      avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
    },
    {
      id: 5,
      title: 'Pitch Request By Nature\'s Voice',
      publication: 'Green Horizons',
      genre: 'Environmental',
      type: 'Blog',
      payout: '₹400',
      submissions: 280,
      status: 'Open',
      description: 'Stories about environmental conservation, sustainable living, and the beauty of nature. Share your experiences and insights about protecting our planet.',
      avatar: 'https://randomuser.me/api/portraits/women/89.jpg'
    }
  ];

  const closedPitchRequests = [
    {
      id: 6,
      title: 'Pitch Request By Writers Weekly',
      publication: 'Midnight Stories',
      genre: 'Horror',
      type: 'Story',
      payout: '₹800',
      submissions: 750,
      status: 'Closed',
      description: 'Dark tales that explore the supernatural and psychological horror. Stories that keep readers awake at night with their haunting narratives.',
      avatar: 'https://randomuser.me/api/portraits/women/25.jpg'
    },
    {
      id: 7,
      title: 'Pitch Request By Travel Tales',
      publication: 'Wanderlust Chronicles',
      genre: 'Travel',
      type: 'Article',
      payout: '₹550',
      submissions: 420,
      status: 'Closed',
      description: 'Share your most memorable travel experiences, hidden gems, and cultural discoveries from around the world.',
      avatar: 'https://randomuser.me/api/portraits/men/58.jpg'
    },
    {
      id: 8,
      title: 'Pitch Request By Food & Culture',
      publication: 'Culinary Heritage',
      genre: 'Food',
      type: 'Blog',
      payout: '₹450',
      submissions: 380,
      status: 'Closed',
      description: 'Explore traditional recipes, food culture, and the stories behind regional cuisines that define our heritage.',
      avatar: 'https://randomuser.me/api/portraits/women/72.jpg'
    },
    {
      id: 9,
      title: 'Pitch Request By Tech Innovators',
      publication: 'Future Forward',
      genre: 'Technology',
      type: 'Article',
      payout: '₹700',
      submissions: 290,
      status: 'Closed',
      description: 'Insights into emerging technologies, startup stories, and innovations that are shaping the future of business.',
      avatar: 'https://randomuser.me/api/portraits/men/41.jpg'
    }
  ];

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleCloseForm = () => {
    setShowCreateForm(false);
    setFormData({
      title: '',
      brief: '',
      contentType: '',
      genre: '',
      deadline: '',
      wordCount: '',
      language: '',
      tags: '',
      guidelines: '',
      requireFirstRights: false,
      showSubmissionCount: false,
      autoCloseDeadline: false
    });
  };

  const handleSubmitForm = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission here
    handleCloseForm();
  };

  const currentPitches = activeTab === 'Open' ? openPitchRequests : closedPitchRequests;

  const filteredPitches = currentPitches.filter(pitch => {
    if (filters.contentType !== 'All' && pitch.type !== filters.contentType) return false;
    if (filters.genre !== 'All' && pitch.genre !== filters.genre) return false;
    if (filters.payout !== 'All' && pitch.payout !== filters.payout) return false;
    return true;
  });

  // Sample submissions data for the modal
  const sampleSubmissions = [
    {
      id: 1,
      author: 'boho_beauty',
      title: "India's Oldest Temple",
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
      story: `On days when work wears you down, I wish you little joys to embrace on your lap. On days when relationships tear you down, I wish you grace to fill the gap. For life isn't always fair and dandy, Some nights are longer than days combined. Sometimes you must pretend to be strong Until pretension manoeuvres the masquerade. I wish you infectious laughter and mirth, I wish for you little moments to redefine the day. I wish for you integrity to stand tall amidst storms, I wish amidst a crowd of maturity, there's a naive child's play.

For life is a road not uphill forever, The crests and troughs and a roller coaster ride. May you find the beauty and the mundaneness of everyday, May your friends add and worries divide. Its seldom easy to bask in the sea of truth, For truth often comes with its own implications, May you feel have to strength to never halt. May you have the yellow of sunshine, The peace of the rustling leaves, the limitlessness of the sky. May your wings devour the wide horizon And keep at it, never tire.

On days it gets difficult to feel positive, I wish you light and love and warmth. On days negativity wears heavy on you, I wish the rain pours on your chaotic swarm. May you be blessed with the lightness of air, May you know death in wrinkles and warm. May love lead your life to solace, May all your good deeds outdo all harm.

For life is a road not uphill forever, The crests and troughs and a roller coaster ride.

On days when work wears you down, I wish you little joys to embrace on your lap. On days when relationships tear you down, I wish you grace to fill the gap.`
    },
    {
      id: 2,
      author: 'curious_kat',
      title: "Ahmedabad's Rutheesing Jain Temple",
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
      story: `The Hutheesing Jain Temple, also known as the Rutheesing Jain Temple, stands as one of the most magnificent examples of Jain architecture in Ahmedabad. Built in 1848 by Sheth Hutheesing, a wealthy Jain merchant, this temple is dedicated to Dharmanatha, the 15th Jain Tirthankara.

The temple's architecture is a stunning blend of traditional Jain design with intricate carvings that tell stories of devotion and spirituality. The main structure rises majestically with its ornate spires reaching towards the heavens, symbolizing the soul's journey towards liberation.

What makes this temple truly special is its detailed stone work. Every pillar, every arch, and every surface is adorned with delicate carvings depicting various Jain symbols, celestial beings, and geometric patterns. The craftsmanship is so fine that it seems almost impossible that human hands could have created such intricate beauty.

The temple complex includes multiple shrines, each housing beautiful marble idols of Jain Tirthankaras. The main shrine contains a stunning idol of Dharmanatha, carved from white marble and adorned with precious stones during special ceremonies.

Visitors often speak of the peaceful atmosphere that pervades the temple grounds. The morning prayers, the gentle chanting, and the soft footsteps of devotees create a symphony of spirituality that touches the heart of every visitor, regardless of their faith.`
    },
    {
      id: 3,
      author: 'wildwanderer',
      title: 'Tales of Tughlaqabad',
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      story: `Tughlaqabad stands today as a haunting reminder of ambitious dreams and their inevitable decay. Built in the 14th century by Ghiyas-ud-din Tughlaq, this fortified city was meant to be the new capital of the Delhi Sultanate, a symbol of power and permanence.

The massive walls of Tughlaqabad stretch for miles, built with a unique technique using large stone blocks without mortar. These walls, some reaching heights of 15 meters, were designed to withstand any siege. The city was planned with meticulous detail - residential areas, markets, mosques, and palaces, all within the protective embrace of these formidable fortifications.

But Tughlaqabad's glory was short-lived. Legend speaks of a curse placed by the Sufi saint Nizamuddin Auliya, who declared that the city would remain uninhabited. Whether by curse or circumstance, the city was abandoned within a few years of its completion, leaving behind only ruins that whisper tales of its brief but magnificent past.

Walking through Tughlaqabad today, one can almost hear the echoes of bustling markets, the calls of merchants, and the footsteps of courtiers. The ruins speak of a time when this was a thriving metropolis, when these walls protected thousands of inhabitants, when these gates welcomed travelers from distant lands.

The tomb of Ghiyas-ud-din Tughlaq stands on an island in the middle of what was once a lake, connected to the main city by a causeway. This tomb, with its sloping walls and distinctive architecture, represents the final resting place of a ruler whose dreams were larger than life itself.`
    },
    {
      id: 4,
      author: 'coffee_lover',
      title: 'The Saga of the Komagata Maru',
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
      story: `The year was 1914, and the steamship Komagata Maru carried within its hull not just 376 passengers, but the hopes and dreams of an entire community seeking a better life. This vessel, chartered by Gurdit Singh, a Sikh entrepreneur, was destined to become a symbol of resistance against discriminatory immigration policies.

The passengers aboard were primarily Sikhs, with some Muslims and Hindus, all British subjects from Punjab seeking to immigrate to Canada. They believed their status as British subjects would grant them entry into any part of the British Empire. However, they were about to encounter the harsh reality of racial discrimination disguised as immigration policy.

When the Komagata Maru arrived in Vancouver's Burrard Inlet on May 23, 1914, it was met not with welcome, but with hostility. The Canadian government, under pressure from white supremacist groups, had implemented the Continuous Journey Regulation, which effectively barred Indian immigration by requiring immigrants to travel directly from their country of origin - an impossible feat given the lack of direct shipping routes.

For two months, the ship remained anchored in the harbor, its passengers trapped between the sea and an unwelcoming shore. The local Sikh community rallied to support their stranded compatriots, providing food and legal assistance. Court battles ensued, with lawyers arguing that as British subjects, these passengers had every right to enter Canada.

The conditions aboard the ship deteriorated rapidly. Food and water supplies dwindled, sanitation became a serious concern, and the psychological toll on the passengers grew heavier with each passing day. Children fell ill, elderly passengers struggled with the confined conditions, and hope began to fade.

On July 23, 1914, after a prolonged legal battle and mounting international pressure, the Canadian government ordered the ship to leave. Only 24 passengers were allowed to remain - those who had previously lived in Canada. The rest were forced to return to India, their dreams shattered but their dignity intact.

The return journey was fraught with tragedy. Upon reaching Calcutta, British authorities, suspicious of the passengers' potential revolutionary activities, attempted to transport them directly to Punjab by train. The passengers, having endured months of humiliation, refused to board. In the ensuing confrontation at Budge Budge, police opened fire, killing 19 passengers and injuring many more.

The Komagata Maru incident became a catalyst for the Indian independence movement. It exposed the hypocrisy of British claims of equality within the Empire and galvanized Indian nationalism. The passengers, who had set out seeking economic opportunities, returned as symbols of resistance against colonial oppression.

Today, the story of the Komagata Maru serves as a powerful reminder of the struggles faced by immigrants and the importance of fighting against discrimination in all its forms. It stands as a testament to the courage of those who dare to dream of a better life, even in the face of seemingly insurmountable obstacles.`
    },
    {
      id: 5,
      author: 'quirkymind',
      title: 'The Story of the Bahmanis',
      submittedOn: 'Jan 19, 2025',
      status: 'Accepted',
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      story: `In the heart of the Deccan plateau, where the red earth meets the azure sky, once stood the mighty Bahmani Sultanate, a kingdom that would reshape the political landscape of medieval India. Founded in 1347 by Alauddin Hasan Bahman Shah, this sultanate emerged from the ashes of rebellion against the Delhi Sultanate, marking the beginning of a new era in South Indian history.

The story begins with Zafar Khan, a Turkish noble who served under Muhammad bin Tughluq. Disillusioned with the erratic policies of the Delhi Sultan and inspired by the desire for independence, he led a successful revolt in the Deccan. Upon establishing his kingdom, he took the title of Alauddin Hasan Bahman Shah, naming his dynasty after his spiritual guide, Hazrat Bahman Baba.

The Bahmani capital of Gulbarga became a beacon of culture and learning. The sultans were great patrons of art, literature, and architecture. They invited scholars, poets, and artisans from Persia, Central Asia, and other parts of India, creating a cosmopolitan atmosphere that fostered intellectual growth and cultural synthesis.

The kingdom's military prowess was legendary. The Bahmanis maintained a formidable army that included war elephants, cavalry, and infantry units. Their strategic location allowed them to control important trade routes, bringing immense wealth that funded their military campaigns and architectural projects.

One of the most remarkable aspects of Bahmani rule was their administrative system. They divided their kingdom into four provinces, each governed by a capable administrator. This decentralized approach allowed for efficient governance while maintaining central authority. The sultans also implemented a unique policy of religious tolerance, employing both Muslims and Hindus in important administrative positions.

The architectural legacy of the Bahmanis is breathtaking. The Jama Masjid in Gulbarga, with its unique design featuring no open courtyard, stands as a testament to their innovative approach to Islamic architecture. The tomb of Ahmad Shah I, with its magnificent dome and intricate calligraphy, showcases the artistic heights achieved during their reign.

However, like many great empires, the Bahmani Sultanate faced internal strife. The rivalry between the Deccanis (local Muslims) and the Afaqis (foreign Muslims) created persistent tensions. These factional disputes weakened the central authority and eventually led to the fragmentation of the kingdom.

The later period saw the capital shift to Bidar, where the sultans built magnificent palaces and gardens. The Bidar Fort, with its impressive fortifications and beautiful palaces, represents the architectural zenith of Bahmani power. The intricate tile work, the sophisticated water management systems, and the harmonious blend of Persian and local architectural elements make it a masterpiece of medieval Indian architecture.

The Bahmani Sultanate's influence extended far beyond its political boundaries. It served as a bridge between North and South India, facilitating cultural exchange and trade. The Persian language flourished under their patronage, and many important literary works were produced during this period.

The kingdom's decline began in the late 15th century when powerful nobles started asserting their independence. By 1518, the Bahmani Sultanate had fragmented into five successor states: Bijapur, Golconda, Ahmednagar, Berar, and Bidar. These kingdoms, known as the Deccan Sultanates, would continue the Bahmani legacy for several more centuries.

The story of the Bahmanis is one of ambition, cultural synthesis, and eventual fragmentation. It reminds us that even the mightiest empires are subject to the inexorable forces of time and change. Yet their contributions to Indian culture, architecture, and administration continue to inspire and educate us about our rich and diverse heritage.

Today, as we walk through the ruins of Gulbarga and Bidar, we can almost hear the echoes of a glorious past - the sound of horses' hooves on cobblestone streets, the calls of merchants in bustling bazaars, and the melodious recitation of poetry in royal courts. The Bahmani Sultanate may have ended, but its legacy lives on in the stones of its monuments and the pages of history.`
    },
    {
      id: 6,
      author: 'storyteller',
      title: 'Mysteries of Hampi',
      submittedOn: 'Jan 18, 2025',
      status: 'Pending',
      avatar: 'https://randomuser.me/api/portraits/men/6.jpg',
      story: `Hampi, the last capital of the mighty Vijayanagara Empire, stands today as a UNESCO World Heritage Site, its ruins scattered across a landscape that seems almost otherworldly. The boulder-strewn terrain, dotted with magnificent temples and royal structures, tells the story of one of India's greatest empires.

The Virupaksha Temple, dedicated to Lord Shiva, has been a center of worship for over 700 years. Its towering gopuram dominates the skyline, while its intricate carvings and pillared halls speak of the devotion and artistic excellence of the Vijayanagara period.

Walking through the Royal Enclosure, one can imagine the grandeur of the Vijayanagara court. The Lotus Mahal, with its Indo-Islamic architecture, the Elephant Stables that once housed the royal elephants, and the stepped tank that supplied water to the royal complex - all bear witness to the sophisticated urban planning of this medieval city.

The Vittala Temple complex, with its famous stone chariot and musical pillars, represents the pinnacle of Vijayanagara architecture. Each pillar, when struck, produces a different musical note, showcasing the incredible skill of the ancient craftsmen.

But Hampi's story is also one of destruction. In 1565, the Battle of Talikota saw the defeat of the Vijayanagara forces by a coalition of Deccan Sultanates. The city was sacked and abandoned, leaving behind the magnificent ruins that continue to awe visitors today.`
    },
    {
      id: 7,
      author: 'heritage',
      title: 'The Forgotten Fort of Chitradurga',
      submittedOn: 'Jan 18, 2025',
      status: 'Reviewing',
      avatar: 'https://randomuser.me/api/portraits/women/7.jpg',
      story: `Chitradurga Fort, known locally as Kallina Kote (Stone Fortress), stands as one of Karnataka's most formidable hill forts. Built between the 15th and 18th centuries by the Nayakas of Chitradurga, this massive fortification sprawls across several hills, creating an almost impregnable defense system.

The fort's most famous legend revolves around Onake Obavva, a brave woman who single-handedly defended the fort against Hyder Ali's forces. Armed only with a pestle (onake), she killed dozens of enemy soldiers who were trying to enter through a secret passage, before being overwhelmed and killed. Her sacrifice has become a symbol of courage and patriotism.

The fort complex includes seven concentric walls, 19 gateways, 38 posterior entrances, a palace, a mosque, granaries, oil pits, four secret entrances, and water tanks. The ingenious water harvesting system, with its network of tanks and channels, ensured water supply even during prolonged sieges.

The Hidimbeshwar Temple within the fort complex is carved entirely out of a single rock, showcasing the architectural prowess of the period. The temple's intricate carvings and the massive Nandi statue are marvels of stone craftsmanship.

Today, Chitradurga Fort stands as a testament to the military engineering skills of medieval India, its massive walls and strategic design continuing to impress visitors and historians alike.`
    },
    {
      id: 8,
      author: 'ancient',
      title: 'Secrets of Sanchi Stupa',
      submittedOn: 'Jan 17, 2025',
      status: 'Rejected',
      avatar: 'https://randomuser.me/api/portraits/men/8.jpg',
      story: `The Great Stupa at Sanchi, commissioned by Emperor Ashoka in the 3rd century BCE, stands as one of the oldest stone structures in India and a masterpiece of Buddhist architecture. This hemispherical dome, rising majestically from a hilltop in Madhya Pradesh, has witnessed over two millennia of history.

Originally built to house the relics of Buddha, the stupa was later enlarged and embellished by subsequent rulers. The four ornate gateways (toranas), added in the 1st century BCE, are adorned with intricate carvings depicting scenes from Buddha's life and Jataka tales.

The eastern gateway tells the story of Buddha's birth and enlightenment, while the southern gateway depicts his first sermon at Sarnath. The western gateway shows the seven weeks Buddha spent in meditation after his enlightenment, and the northern gateway illustrates the miracle at Shravasti.

What makes Sanchi unique is that Buddha is never depicted in human form in these early carvings. Instead, his presence is symbolized through footprints, the Bodhi tree, the wheel of dharma, or an empty throne, reflecting the early Buddhist reluctance to anthropomorphize the Buddha.

The site was abandoned for nearly 600 years before being rediscovered by British archaeologist Alexander Cunningham in 1818. The careful restoration work has preserved this magnificent monument for future generations, making it a UNESCO World Heritage Site and a pilgrimage destination for Buddhists worldwide.`
    }
  ];

  const handleViewDetails = (pitch) => {
    setSelectedPitch(pitch);
    setShowDetailsView(true);
    setShowCreateForm(false);
    setShowStoryView(false);
  };

  const handleViewStory = (submission) => {
    setSelectedSubmission(submission);
    setShowStoryView(true);
    setShowDetailsView(false);
  };

  const handleBackToList = () => {
    setShowDetailsView(false);
    setShowStoryView(false);
    setSelectedPitch(null);
    setSelectedSubmission(null);
  };

  const handleBackToDetails = () => {
    setShowStoryView(false);
    setSelectedSubmission(null);
  };

  return (
    <div className="p-6 bg-gradient-to-br from-slate-50 to-slate-200 min-h-screen relative overflow-y-auto overflow-x-hidden opacity-0" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
      {/* Header, Tabs, and Filters - Hide when create form, details view, or story view is shown */}
      {!showCreateForm && !showDetailsView && !showStoryView && (
        <>
          {/* Header */}
          <div className="flex justify-between items-center mb-8 relative z-10 opacity-0 -translate-y-4" style={{ animation: 'slideInUp 0.6s ease-out 0.1s forwards' }}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent relative">
              Pitch Hub
              <div className="absolute -bottom-2 left-0 w-15 h-1 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full"></div>
            </h1>
            <button
              className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-blue-500/40 relative overflow-hidden group"
              onClick={handleCreateNew}
            >
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
              Create New
            </button>
          </div>

          {/* Tabs */}
          <div className="flex gap-2 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.2s forwards' }}>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 relative overflow-hidden group ${
                activeTab === 'Open'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Open')}
            >
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
              Open
            </button>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 relative overflow-hidden group ${
                activeTab === 'Closed'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Closed')}
            >
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
              Closed
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-8 opacity-0 translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.3s forwards' }}>
        <select
          value={filters.contentType}
          onChange={(e) => handleFilterChange('contentType', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Content Type</option>
          <option value="Blog">Blog</option>
          <option value="Story">Story</option>
          <option value="Article">Article</option>
        </select>

        <select
          value={filters.genre}
          onChange={(e) => handleFilterChange('genre', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Genre</option>
          <option value="Romance">Romance</option>
          <option value="Fantasy">Fantasy</option>
          <option value="Urban Life">Urban Life</option>
          <option value="Science Fiction">Science Fiction</option>
          <option value="Environmental">Environmental</option>
        </select>

        <select
          value={filters.language}
          onChange={(e) => handleFilterChange('language', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Language</option>
          <option value="English">English</option>
          <option value="Hindi">Hindi</option>
          <option value="Bengali">Bengali</option>
        </select>

        <select
          value={filters.payout}
          onChange={(e) => handleFilterChange('payout', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Payout</option>
          <option value="₹400">₹400</option>
          <option value="₹500">₹500</option>
          <option value="₹600">₹600</option>
          <option value="₹750">₹750</option>
        </select>
      </div>
        </>
      )}

      {/* Main Content Area */}
      {showDetailsView ? (
        /* Details View */
        <div className="opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          {/* Back Button */}
          <div className="mb-8">
            <button
              onClick={handleBackToList}
              className="group flex items-center gap-3 px-6 py-3 bg-white/80 hover:bg-white backdrop-blur-sm rounded-xl transition-all duration-300 text-gray-700 hover:text-blue-600 shadow-lg hover:shadow-xl hover:-translate-y-1 border border-gray-200 hover:border-blue-200"
            >
              <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white flex items-center justify-center group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                  <path d="M19 12H5M12 19l-7-7 7-7"/>
                </svg>
              </div>
              <span className="font-semibold">Back to Pitches</span>
            </button>
          </div>
          
          {/* Details Content */}
          <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-200 overflow-hidden">
            {/* Header */}
            <div className="p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {selectedPitch?.title}
              </h2>
              <p className="text-gray-600 mt-1">Submissions Overview</p>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Pitch Info */}
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-2xl p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch?.genre}</div>
                    <div className="text-sm text-gray-600">Genre</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch?.type}</div>
                    <div className="text-sm text-gray-600">Type</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{selectedPitch?.payout}</div>
                    <div className="text-sm text-gray-600">Payout</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{sampleSubmissions.length}</div>
                    <div className="text-sm text-gray-600">Total Submissions</div>
                  </div>
                </div>
              </div>

              {/* Submissions Table */}
              <div className="bg-white/95 backdrop-blur-xl rounded-2xl border border-blue-200 shadow-blue-100 overflow-hidden">
                {/* Table Header */}
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
                  <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                    <div className="col-span-3">Author</div>
                    <div className="col-span-3">Title</div>
                    <div className="col-span-2">Submitted On</div>
                    <div className="col-span-2">Status</div>
                    <div className="col-span-2">Action</div>
                  </div>
                </div>

                {/* Table Body */}
                <div className="divide-y divide-gray-100 max-h-96 overflow-y-auto">
                  {sampleSubmissions.map((submission) => (
                    <div
                      key={submission.id}
                      className="px-6 py-4 hover:bg-blue-50/50 transition-all duration-300"
                    >
                      <div className="grid grid-cols-12 gap-4 items-center">
                        {/* Author */}
                        <div className="col-span-3 flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-blue-200 flex-shrink-0">
                            <img
                              src={submission.avatar}
                              alt="Author"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div>
                            <p className="font-semibold text-gray-800">{submission.author}</p>
                          </div>
                        </div>

                        {/* Title */}
                        <div className="col-span-3">
                          <p className="font-medium text-gray-800 truncate">{submission.title}</p>
                        </div>

                        {/* Submitted On */}
                        <div className="col-span-2">
                          <span className="text-gray-700">{submission.submittedOn}</span>
                        </div>

                        {/* Status */}
                        <div className="col-span-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                            submission.status === 'Accepted'
                              ? 'bg-green-100 text-green-700'
                              : submission.status === 'Pending'
                              ? 'bg-yellow-100 text-yellow-700'
                              : submission.status === 'Rejected'
                              ? 'bg-red-100 text-red-700'
                              : 'bg-blue-100 text-blue-700'
                          }`}>
                            {submission.status}
                          </span>
                        </div>

                        {/* Action */}
                        <div className="col-span-2">
                          <button
                            onClick={() => handleViewStory(submission)}
                            className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-0.5"
                          >
                            View
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : showStoryView ? (
        /* Story View - Replace ads outlet with submission overview */
        <div className="opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          {/* CSS to hide ads outlet and adjust layout */}
          <style>{`
            /* Hide the original ads outlet when story view is active */
            body:has([data-story-view="true"]) .md\\:mr-\\[250px\\] {
              margin-right: 0 !important;
            }
            body:has([data-story-view="true"]) [class*="md:fixed"][class*="right-0"][class*="w-\\[250px\\]"]:not(.submission-overview) {
              display: none !important;
            }
          `}</style>

          {/* Submission Overview Outlet replacing ads space */}
          <div className="submission-overview fixed right-0 top-0 w-[250px] overflow-y-auto bg-white border-l-2 border-blue-200 shadow-2xl z-[10000]"
               style={{ height: "calc(100% - 64.67px)", top: "64.67px", display: "flex", flexDirection: "column" }}>
            {/* Submission Overview Header */}
            <div className="sticky top-0 bg-gradient-to-r from-blue-500 to-blue-600 border-b border-blue-300 p-4 z-10">
              <h3 className="text-sm font-bold text-white">Submission Overview</h3>
            </div>

            {/* Scrollable Submission Overview Content */}
            <div className="flex-1 p-4 space-y-4">
              {/* Box 1: Details */}
              <div className="bg-white rounded-xl shadow-xl border-2 border-blue-300 overflow-hidden transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-blue-500 to-indigo-500 p-3 border-b border-blue-400">
                  <h3 className="text-sm font-bold text-white">📋 Details</h3>
                </div>
                <div className="p-3 space-y-3">
                  {/* Content Type */}
                  <div className="border-b border-gray-100 pb-2">
                    <div className="text-xs font-semibold text-blue-600 mb-1">Content Type:</div>
                    <div className="text-gray-700 text-sm">Short Story</div>
                  </div>

                  {/* Language */}
                  <div className="border-b border-gray-100 pb-2">
                    <div className="text-xs font-semibold text-blue-600 mb-1">Language:</div>
                    <div className="text-gray-700 text-sm">English</div>
                  </div>

                  {/* Genre */}
                  <div className="border-b border-gray-100 pb-2">
                    <div className="text-xs font-semibold text-blue-600 mb-1">Genre:</div>
                    <div className="text-gray-700 text-sm">Mystery, Thriller</div>
                  </div>

                  {/* Word Count */}
                  <div className="border-b border-gray-100 pb-2">
                    <div className="text-xs font-semibold text-blue-600 mb-1">Word Count:</div>
                    <div className="text-gray-700 text-sm">1200 Words</div>
                  </div>

                  {/* Reading Level */}
                  <div className="border-b border-gray-100 pb-2">
                    <div className="text-xs font-semibold text-blue-600 mb-1">Reading Level:</div>
                    <div className="text-gray-700 text-sm">Basic</div>
                  </div>

                  {/* Copyright Information */}
                  <div>
                    <div className="text-xs font-semibold text-blue-600 mb-1">Copyright:</div>
                    <div className="text-gray-700 text-xs">© 2025 by Neha Pramod. All rights reserved.</div>
                  </div>
                </div>
              </div>

              {/* Box 2: Evaluation & Feedback */}
              <div className="bg-white rounded-xl shadow-xl border-2 border-purple-300 overflow-hidden transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-purple-500 to-indigo-500 p-3 border-b border-purple-400">
                  <h3 className="text-sm font-bold text-white">📝 Evaluation & Feedback</h3>
                </div>
                <div className="p-3 space-y-3">
                  {/* Current Status */}
                  <div>
                    <div className="text-xs font-semibold text-purple-600 mb-1">Current Status:</div>
                    <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs">
                      <option>select</option>
                      <option>Under Review</option>
                      <option>Accepted</option>
                      <option>Rejected</option>
                      <option>Needs Revision</option>
                    </select>
                  </div>

                  {/* Feedback */}
                  <div>
                    <div className="text-xs font-semibold text-purple-600 mb-1">Feedback:</div>
                    <textarea
                      placeholder="feedback to author"
                      className="w-full px-2 py-1 border border-gray-300 rounded resize-none h-16 text-xs"
                    />
                  </div>

                  {/* Feedback Note */}
                  <div className="text-xs text-red-500 italic">
                    Receive 50% Of The Submission Fee By Providing Timely Feedback.
                  </div>

                  {/* Share Feedback Button */}
                  <button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-2 py-1 rounded text-xs font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300">
                    Share Feedback
                  </button>
                </div>
              </div>

              {/* Box 3: Decision & Payout */}
              <div className="bg-white rounded-xl shadow-xl border-2 border-green-300 overflow-hidden transform hover:scale-105 transition-all duration-300">
                <div className="bg-gradient-to-r from-green-500 to-emerald-500 p-3 border-b border-green-400">
                  <h3 className="text-sm font-bold text-white">💰 Decision & Payout</h3>
                </div>
                <div className="p-3 space-y-2">
                  {/* Budget */}
                  <div>
                    <div className="text-xs font-semibold text-green-600 mb-1">Budget:</div>
                    <div className="text-gray-700 text-xs">Rs. 500 - Rs. 1000</div>
                  </div>

                  {/* Quote */}
                  <div>
                    <div className="text-xs font-semibold text-green-600 mb-1">Quote:</div>
                    <div className="text-gray-700 text-xs">Rs. 1000</div>
                  </div>

                  {/* Skrivee Service Fee */}
                  <div>
                    <div className="text-xs font-semibold text-green-600 mb-1">Service Fee (5%):</div>
                    <div className="text-gray-700 text-xs">Rs. 50</div>
                  </div>

                  {/* Total Amount */}
                  <div className="border-t border-gray-200 pt-2">
                    <div className="text-xs font-semibold text-green-600 mb-1">Total amount:</div>
                    <div className="text-sm font-bold text-green-600">Rs. 1050</div>
                  </div>

                  {/* Next Steps */}
                  <div>
                    <div className="text-xs font-semibold text-green-600 mb-2">Next Steps:</div>
                    <div className="flex gap-1">
                      <button className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white px-2 py-1 rounded text-xs font-semibold hover:from-green-600 hover:to-green-700 transition-all duration-300">
                        Accept
                      </button>
                      <button className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white px-2 py-1 rounded text-xs font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300">
                        Reject
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Add more space at bottom for better scrolling */}
              <div className="h-8"></div>
            </div>
          </div>

          <div data-story-view="true" className="pr-4">
            {/* Back Button */}
            <div className="mb-6">
              <button
                onClick={handleBackToDetails}
                className="group flex items-center gap-3 px-6 py-3 bg-white/80 hover:bg-white backdrop-blur-sm rounded-xl transition-all duration-300 text-gray-700 hover:text-blue-600 shadow-lg hover:shadow-xl hover:-translate-y-1 border border-gray-200 hover:border-blue-200"
              >
                <div className="w-8 h-8 rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white flex items-center justify-center group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-300">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5">
                    <path d="M19 12H5M12 19l-7-7 7-7"/>
                  </svg>
                </div>
                <span className="font-semibold">Back to Submissions</span>
              </button>
            </div>

          {/* Main Layout - Content with right margin for submission overview */}
          <div className="w-full mr-[270px] max-w-[calc(100%-290px)]">
              {/* Banner Section - Adjusted width to fit properly */}
              <div className="relative h-80 overflow-hidden rounded-2xl shadow-2xl mb-6 w-full">
                {/* Background Image */}
                <div
                  className="absolute inset-0 bg-cover bg-center"
                  style={{
                    backgroundImage: "url('https://images.unsplash.com/photo-1519904981063-b0cf448d479e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80')"
                  }}
                />
                {/* Dark Overlay */}
                <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/50 to-black/70" />

                {/* Content */}
                <div className="absolute inset-0 flex flex-col justify-center px-8">
                  <h1 className="text-4xl md:text-5xl font-bold text-white mb-4 drop-shadow-2xl">
                    {selectedSubmission?.title || "a Horror Story That Sends Chills!"}
                  </h1>
                </div>
              </div>

              {/* Author and Status Info */}
              <div className="bg-white rounded-2xl shadow-lg p-6 mb-6 w-full">
                <div className="grid grid-cols-3 gap-4">
                  {/* Author */}
                  <div>
                    <div className="text-sm text-gray-600 mb-1">Author:</div>
                    <div className="flex items-center gap-3">
                      <img
                        src={selectedSubmission?.avatar || 'https://randomuser.me/api/portraits/women/44.jpg'}
                        alt="Author"
                        className="w-10 h-10 rounded-full"
                      />
                      <span className="font-semibold text-gray-800">{selectedSubmission?.author || "Neha Pramod"}</span>
                    </div>
                  </div>

                  {/* Status */}
                  <div>
                    <div className="text-sm text-gray-600 mb-1">Status:</div>
                    <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                      selectedSubmission?.status === 'Accepted'
                        ? 'bg-green-100 text-green-700'
                        : selectedSubmission?.status === 'Pending'
                        ? 'bg-yellow-100 text-yellow-700'
                        : selectedSubmission?.status === 'Rejected'
                        ? 'bg-red-100 text-red-700'
                        : 'bg-blue-100 text-blue-700'
                    }`}>
                      {selectedSubmission?.status || "Under Review"}
                    </span>
                  </div>

                  {/* Date Submitted */}
                  <div>
                    <div className="text-sm text-gray-600 mb-1">Date Submitted:</div>
                    <span className="font-semibold text-gray-800">{selectedSubmission?.submittedOn || "December 19, 2024"}</span>
                  </div>
                </div>
              </div>

              {/* Story Content with Scroller */}
              <div className="bg-white rounded-2xl shadow-lg p-6 w-full">
                <div className="max-h-96 overflow-y-auto pr-4 custom-scrollbar">
                  <div className="text-gray-800 leading-relaxed whitespace-pre-line text-justify">
                    {selectedSubmission?.story || `On days when work wears you down, I wish you little joys to embrace on your lap. On days when relationships tear you down, I wish you grace to fill the gap. For life isn't always fair and dandy, Some nights are longer than days combined. Sometimes you must pretend to be strong Until pretension manoeuvres the masquerade. I wish you infectious laughter and mirth, I wish for you little moments to redefine the day. I wish for you integrity to stand tall amidst storms, I wish amidst a crowd of maturity, there's a naive child's play.

For life is a road not uphill forever, The crests and troughs and a roller coaster ride. May you find the beauty and the mundaneness of everyday, May your friends add and worries divide. Its seldom easy to bask in the sea of truth, For truth often comes with its own implications, May you feel have to strength to never halt. May you have the yellow of sunshine, The peace of the rustling leaves, the limitlessness of the sky. May your wings devour the wide horizon And keep at it, never tire.

On days it gets difficult to feel positive, I wish you light and love and warmth. On days negativity wears heavy on you, I wish the rain pours on your chaotic swarm. May you be blessed with the lightness of air, May you know death in wrinkles and warm. May love lead your life to solace, May all your good deeds outdo all harm.

For life is a road not uphill forever, The crests and troughs and a roller coaster ride.

On days when work wears you down, I wish you little joys to embrace on your lap. On days when relationships tear you down, I wish you grace to fill the gap.`}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : !showCreateForm ? (
        /* Pitch List */
        <div className="flex flex-col gap-6 opacity-0" style={{ animation: 'fadeInUp 0.3s ease-out forwards' }} key={activeTab}>
          {filteredPitches.map((pitch, index) => (
          <div
            key={pitch.id}
            className={`bg-white/95 backdrop-blur-xl rounded-2xl p-6 border transition-all duration-300 relative overflow-hidden opacity-0 translate-y-8 hover:scale-105 group ${
              pitch.status.toLowerCase() === 'closed'
                ? 'border-red-100 shadow-red-100 hover:border-red-200 hover:shadow-red-200'
                : 'border-blue-100 shadow-blue-100 hover:border-blue-200 hover:shadow-blue-200'
            }`}
            style={{
              animationDelay: `${index * 0.1}s`,
              animation: 'slideInUp 0.6s ease-out forwards',
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.08)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-800"></div>
            <div className="flex justify-between items-start mb-5 relative z-10">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-800 mb-3 leading-tight">{pitch.title}</h3>
                <div className="flex items-center gap-3">
                  <img
                    src={pitch.avatar}
                    alt={pitch.publication}
                    className="w-10 h-10 rounded-full border-2 border-blue-200 transition-all duration-300 hover:scale-110 hover:border-blue-500"
                  />
                  <span className="font-semibold text-gray-700">{pitch.publication}</span>
                </div>
              </div>
              <span className={`px-3 py-1.5 rounded-full text-sm font-semibold uppercase tracking-wide ${
                pitch.status.toLowerCase() === 'open'
                  ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg shadow-green-500/30'
                  : 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/30'
              }`}>
                {pitch.status}
              </span>
            </div>

            <div className="mb-5 relative z-10">
              <div className="flex gap-8 mb-3">
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Genre:</span>
                  <span className="text-gray-800 font-medium">{pitch.genre}</span>
                </div>
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Type:</span>
                  <span className="text-gray-800 font-medium">{pitch.type}</span>
                </div>
              </div>
              <div className="flex gap-8">
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Payout:</span>
                  <span className="text-gray-800 font-medium">{pitch.payout}</span>
                </div>
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Submissions:</span>
                  <span className="text-gray-800 font-medium">{pitch.submissions}</span>
                </div>
              </div>
            </div>

            <div className="mb-6 relative z-10">
              <p className="text-gray-700 leading-relaxed text-sm">{pitch.description}</p>
            </div>

            <div className="flex justify-end relative z-10">
              <button
                onClick={() => handleViewDetails(pitch)}
                className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden group"
              >
                <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                View Details
              </button>
            </div>
          </div>
          ))}
        </div>
      ) : (
        /* Create New Pitch Form */
        <div className="relative z-10 opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          <div className="bg-white/98 backdrop-blur-xl rounded-3xl p-8 w-full shadow-2xl shadow-blue-500/20 border border-blue-100 relative mb-6">
            <div className="flex justify-between items-center mb-6 pb-5 border-b-2 border-blue-100 relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Create Submission Guidelines
              </h2>
              <button
                className="bg-red-50 hover:bg-red-100 border-none rounded-xl w-11 h-11 flex items-center justify-center cursor-pointer transition-all duration-300 text-red-500 hover:text-red-600 hover:-translate-y-1 hover:shadow-lg hover:shadow-red-200"
                onClick={handleCloseForm}
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
              <div className="absolute bottom-0 left-0 w-20 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full"></div>
            </div>

            <div className="mb-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 rounded-xl">
              <p className="text-gray-700 leading-relaxed">
                Set up your submission requirements to invite Authors to share their best work. Define what you're looking for and the qualifications Authors need to meet.
              </p>
            </div>

            <form className="flex flex-col gap-6" onSubmit={handleSubmitForm}>
              <div className="flex flex-col gap-2">
                <label className="font-semibold text-gray-800 text-base">Title:</label>
                <input
                  type="text"
                  className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                  placeholder="Enter title"
                  value={formData.title}
                  onChange={(e) => handleFormChange('title', e.target.value)}
                  required
                />
              </div>

              <div className="flex flex-col gap-2">
                <label className="font-semibold text-gray-800 text-base">Brief:</label>
                <input
                  type="text"
                  className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                  placeholder="Enter brief description"
                  value={formData.brief}
                  onChange={(e) => handleFormChange('brief', e.target.value)}
                  required
                />
              </div>

              <div className="flex gap-5">
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Content Type</label>
                  <select
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.contentType}
                    onChange={(e) => handleFormChange('contentType', e.target.value)}
                    required
                  >
                    <option value="">select</option>
                    <option value="Blog">Blog</option>
                    <option value="Story">Story</option>
                    <option value="Article">Article</option>
                    <option value="Poetry">Poetry</option>
                  </select>
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Genre</label>
                  <select
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.genre}
                    onChange={(e) => handleFormChange('genre', e.target.value)}
                    required
                  >
                    <option value="">select</option>
                    <option value="Romance">Romance</option>
                    <option value="Fantasy">Fantasy</option>
                    <option value="Mystery">Mystery</option>
                    <option value="Science Fiction">Science Fiction</option>
                    <option value="Horror">Horror</option>
                    <option value="Travel">Travel</option>
                    <option value="Food">Food</option>
                    <option value="Technology">Technology</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-5">
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Deadline</label>
                  <input
                    type="date"
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.deadline}
                    onChange={(e) => handleFormChange('deadline', e.target.value)}
                    required
                  />
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Word Count</label>
                  <input
                    type="number"
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    placeholder="Enter word count"
                    value={formData.wordCount}
                    onChange={(e) => handleFormChange('wordCount', e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="flex gap-5">
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Language</label>
                  <select
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    required
                  >
                    <option value="">select</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                  </select>
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Tags</label>
                  <input
                    type="text"
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    placeholder="Enter tags"
                    value={formData.tags}
                    onChange={(e) => handleFormChange('tags', e.target.value)}
                  />
                </div>
              </div>

              <div className="flex flex-col gap-2">
                <label className="font-semibold text-gray-800 text-base">Submission Guidelines</label>
                <div className="text-sm text-gray-600 mb-2">Describe the specific qualities, themes, or styles you expect in submitted drafts</div>
                <textarea
                  className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5 resize-vertical min-h-36 leading-relaxed"
                  placeholder="Outline what you're looking for in a submission — tone, themes, structure, or any dos and don'ts."
                  value={formData.guidelines}
                  onChange={(e) => handleFormChange('guidelines', e.target.value)}
                  rows="4"
                  required
                />
                <div className="mt-3 p-3 bg-blue-50 border-l-3 border-blue-500 rounded-lg">
                  <em className="text-sm text-gray-700">Tip: Clearly define your content requirements, themes or guidelines to help authors submit work that aligns with your publication's needs.</em>
                </div>
              </div>

              {/* Checkbox Options */}
              <div className="flex flex-col gap-4 mt-2">
                <label className="flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300 bg-blue-50/50 border border-blue-100 hover:bg-blue-50 hover:border-blue-200 hover:-translate-y-0.5">
                  <input
                    type="checkbox"
                    className="w-5 h-5 accent-blue-500 cursor-pointer"
                    checked={formData.requireFirstRights}
                    onChange={(e) => handleFormChange('requireFirstRights', e.target.checked)}
                  />
                  <span className="text-base text-gray-800 font-medium">Require first Publication Rights</span>
                </label>

                <label className="flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300 bg-blue-50/50 border border-blue-100 hover:bg-blue-50 hover:border-blue-200 hover:-translate-y-0.5">
                  <input
                    type="checkbox"
                    className="w-5 h-5 accent-blue-500 cursor-pointer"
                    checked={formData.showSubmissionCount}
                    onChange={(e) => handleFormChange('showSubmissionCount', e.target.checked)}
                  />
                  <span className="text-base text-gray-800 font-medium">Show number of Submissions</span>
                </label>

                <label className="flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300 bg-blue-50/50 border border-blue-100 hover:bg-blue-50 hover:border-blue-200 hover:-translate-y-0.5">
                  <input
                    type="checkbox"
                    className="w-5 h-5 accent-blue-500 cursor-pointer"
                    checked={formData.autoCloseDeadline}
                    onChange={(e) => handleFormChange('autoCloseDeadline', e.target.checked)}
                  />
                  <span className="text-base text-gray-800 font-medium">Auto-Close after Deadline</span>
                </label>
              </div>

              {/* Make Guidelines Live Section */}
              <div className="mt-8 mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-2xl text-center">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                  Make Your Guidelines Live
                </h3>
                <p className="text-base text-gray-700 leading-relaxed">
                  Everything's ready! Publish your submission guidelines to start receiving high-quality drafts from talented writers.
                </p>
              </div>

              <div className="flex gap-4 justify-center mt-8 pt-6 border-t border-blue-100">
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-gray-300 bg-transparent text-gray-700 rounded-xl font-semibold cursor-pointer transition-all duration-300 hover:border-gray-500 hover:text-gray-800 hover:-translate-y-0.5"
                >
                  Save As Draft
                </button>
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-blue-500 bg-transparent text-blue-500 rounded-xl font-semibold cursor-pointer transition-all duration-300 hover:bg-blue-50 hover:-translate-y-0.5"
                >
                  Preview
                </button>
                <button
                  type="submit"
                  className="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-700 text-white border-none rounded-xl font-semibold cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-blue-500/40 relative overflow-hidden group"
                >
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                  Set Live
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Pitch;
