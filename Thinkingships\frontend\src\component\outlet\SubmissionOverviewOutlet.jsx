import React, { useEffect } from "react";
import { Outlet } from "react-router-dom";

function SubmissionOverviewOutletComponent() {
  useEffect(() => {
    console.log("submission overview mounting check");
    return () => console.log("submission overview unmounting");
  }, []);

  return (
    <div className="flex flex-1">
      <div
        className="hidden md:fixed md:flex md:flex-col right-0 top-0 w-[250px] overflow-y-auto bg-gray-50 border-l border-gray-200 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        style={{ height: "calc(100% - 64.67px)", top: "64.67px" }}
      >
        {/* Submission Overview Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-3 z-10">
          <h3 className="text-sm font-semibold text-gray-700">Submission Overview</h3>
        </div>

        {/* Scrollable Submission Overview Content */}
        <div className="flex-1 p-3">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden h-full">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-blue-100">
              <h3 className="text-lg font-bold text-blue-600">Details</h3>
            </div>

            <div className="h-[calc(100%-60px)] overflow-y-auto p-4 space-y-4">
              {/* Content Type */}
              <div className="border-b border-gray-100 pb-3">
                <div className="text-xs font-semibold text-blue-600 mb-1">Content Type:</div>
                <div className="text-gray-700 text-sm">Short Story</div>
              </div>

              {/* Language */}
              <div className="border-b border-gray-100 pb-3">
                <div className="text-xs font-semibold text-blue-600 mb-1">Language:</div>
                <div className="text-gray-700 text-sm">English</div>
              </div>

              {/* Genre */}
              <div className="border-b border-gray-100 pb-3">
                <div className="text-xs font-semibold text-blue-600 mb-1">Genre:</div>
                <div className="text-gray-700 text-sm">Mystery, Thriller</div>
              </div>

              {/* Word Count */}
              <div className="border-b border-gray-100 pb-3">
                <div className="text-xs font-semibold text-blue-600 mb-1">Word Count:</div>
                <div className="text-gray-700 text-sm">1200 Words</div>
              </div>

              {/* Reading Level */}
              <div className="border-b border-gray-100 pb-3">
                <div className="text-xs font-semibold text-blue-600 mb-1">Reading Level:</div>
                <div className="text-gray-700 text-sm">Basic</div>
              </div>

              {/* Copyright Information */}
              <div className="border-b border-gray-100 pb-3">
                <div className="text-xs font-semibold text-blue-600 mb-1">Copyright:</div>
                <div className="text-gray-700 text-xs">© 2025 by Neha Pramod. All rights reserved.</div>
              </div>

              {/* Evaluation & Feedback Section */}
              <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg p-3 border border-purple-100">
                <h4 className="text-sm font-bold text-purple-600 mb-3">Evaluation & Feedback</h4>

                {/* Current Status */}
                <div className="mb-3">
                  <div className="text-xs font-semibold text-purple-600 mb-1">Current Status:</div>
                  <select className="w-full px-2 py-1 border border-gray-300 rounded text-xs">
                    <option>select</option>
                    <option>Under Review</option>
                    <option>Accepted</option>
                    <option>Rejected</option>
                    <option>Needs Revision</option>
                  </select>
                </div>

                {/* Feedback */}
                <div className="mb-3">
                  <div className="text-xs font-semibold text-purple-600 mb-1">Feedback:</div>
                  <textarea
                    placeholder="feedback to author"
                    className="w-full px-2 py-1 border border-gray-300 rounded resize-none h-16 text-xs"
                  />
                </div>

                {/* Feedback Note */}
                <div className="text-xs text-red-500 mb-3 italic">
                  Receive 50% Of The Submission Fee By Providing Timely Feedback.
                </div>

                {/* Share Feedback Button */}
                <button className="w-full bg-gradient-to-r from-blue-500 to-blue-600 text-white px-2 py-1 rounded text-xs font-semibold hover:from-blue-600 hover:to-blue-700 transition-all duration-300">
                  Share Feedback
                </button>
              </div>

              {/* Decision & Payout Section */}
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-3 border border-green-100">
                <h4 className="text-sm font-bold text-blue-600 mb-3">Decision & Payout</h4>

                {/* Budget */}
                <div className="mb-2">
                  <div className="text-xs font-semibold text-blue-600 mb-1">Budget:</div>
                  <div className="text-gray-700 text-xs">Rs. 500 - Rs. 1000</div>
                </div>

                {/* Quote */}
                <div className="mb-2">
                  <div className="text-xs font-semibold text-blue-600 mb-1">Quote:</div>
                  <div className="text-gray-700 text-xs">Rs. 1000</div>
                </div>

                {/* Skrivee Service Fee */}
                <div className="mb-2">
                  <div className="text-xs font-semibold text-blue-600 mb-1">Service Fee (5%):</div>
                  <div className="text-gray-700 text-xs">Rs. 50</div>
                </div>

                {/* Total Amount */}
                <div className="mb-3 border-t border-gray-200 pt-2">
                  <div className="text-xs font-semibold text-blue-600 mb-1">Total amount:</div>
                  <div className="text-sm font-bold text-blue-600">Rs. 1050</div>
                </div>

                {/* Next Steps */}
                <div>
                  <div className="text-xs font-semibold text-blue-600 mb-2">Next Steps:</div>
                  <div className="flex gap-1">
                    <button className="flex-1 bg-gradient-to-r from-green-500 to-green-600 text-white px-2 py-1 rounded text-xs font-semibold hover:from-green-600 hover:to-green-700 transition-all duration-300">
                      Accept
                    </button>
                    <button className="flex-1 bg-gradient-to-r from-red-500 to-red-600 text-white px-2 py-1 rounded text-xs font-semibold hover:from-red-600 hover:to-red-700 transition-all duration-300">
                      Reject
                    </button>
                  </div>
                </div>
              </div>

              {/* Add more space at bottom for better scrolling */}
              <div className="h-4"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="md:mr-[250px] md:pr-6 pr-0 flex-1 md:mt-8 w-full">
        <Outlet />
      </div>
    </div>
  );
}

const SubmissionOverviewOutlet = React.memo(SubmissionOverviewOutletComponent);
export default SubmissionOverviewOutlet;
