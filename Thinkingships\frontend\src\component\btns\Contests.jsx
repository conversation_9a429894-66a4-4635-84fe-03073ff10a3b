import React from 'react'

const contests = (color, h, w, text) => {
    return (
        <div className="flex gap-3 rounded-md cursor-pointer transition-all">
            <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
                <path d="M15 2L18.09 8.26L25 9L20 13.74L21.18 20.5L15 17.27L8.82 20.5L10 13.74L5 9L11.91 8.26L15 2Z" fill={color} />
                <path d="M7 25H23C24.1 25 25 25.9 25 27C25 28.1 24.1 29 23 29H7C5.9 29 5 28.1 5 27C5 25.9 5.9 25 7 25Z" fill={color} />
                <path d="M9 22H21V25H9V22Z" fill={color} />
            </svg>
            <span className="text-sm font-medium text-gray-800">{text}</span>
        </div>
    )
}

function Contests({color, h, w, txt = null}) {
  return (
    <>
    <div className="md:hidden flex">
        <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 30 30" fill="none">
            <path d="M15 2L18.09 8.26L25 9L20 13.74L21.18 20.5L15 17.27L8.82 20.5L10 13.74L5 9L11.91 8.26L15 2Z" fill={color} />
            <path d="M7 25H23C24.1 25 25 25.9 25 27C25 28.1 24.1 29 23 29H7C5.9 29 5 28.1 5 27C5 25.9 5.9 25 7 25Z" fill={color} />
            <path d="M9 22H21V25H9V22Z" fill={color} />
        </svg>
    </div>
    <div className="hidden md:flex">
        {contests(color, h, w, txt)}
    </div>
    </>
  )
}

export default Contests
