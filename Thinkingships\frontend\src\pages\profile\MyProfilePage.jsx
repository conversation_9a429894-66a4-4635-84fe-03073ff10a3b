import { useState } from 'react';
import { BiGlobe } from 'react-icons/bi';
import { FaFacebook, FaHeart, FaInstagram, Fa<PERSON><PERSON><PERSON>in, FaTwitter } from 'react-icons/fa';
import { SiGoodreads } from 'react-icons/si';
import { useNavigate } from 'react-router-dom';

function MyProfilePage({ user }) {
  const navigate = useNavigate();
  const [showFansModal, setShowFansModal] = useState(false);
  const [currentView, setCurrentView] = useState('info'); // 'info', 'fans', 'faves', 'skrivee'
  const [activeContentTab, setActiveContentTab] = useState('Stories'); // 'Stories', 'Poems', 'Blogs', 'e-books'
  const [activeTestimonialTab, setActiveTestimonialTab] = useState('Approved'); // 'Approved', 'Review'

  // Sample fans data
  const fansData = [
    {
      id: 1,
      name: '<PERSON>',
      username: '@ralph_edwards',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 2,
      name: 'Esther Howard',
      username: '@esther_howard',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 3,
      name: 'Darlene Robertson',
      username: '@darlene_robertson',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 4,
      name: 'Eleanor Pena',
      username: '@eleanor_pena',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 5,
      name: 'Wade Warren',
      username: '@wade_warren',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 6,
      name: 'Annette Black',
      username: '@annette_black',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 7,
      name: 'Jacob Jones',
      username: '@jacob_jones',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 8,
      name: 'Theresa Webb',
      username: '@theresa_webb',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    }
  ];

  // Sample content data for Ravi Agarwal's posts
  const myContent = {
    Stories: [
      {
        id: 1,
        title: "Echoes Of The Forgotten",
        author: "Ravi Agarwal",
        date: "Oct 27, 2020",
        genre: "Romance",
        tags: ["#isseehat", "#mysteries", "#adventure", "#adventure"],
        excerpt: "A journalist stumbles upon an abandoned house with diary entries from a girl who vanished decades ago. As he follows the clues, he realizes her story is eerily linked to his own past—one he never knew existed.",
        image: "https://picsum.photos/800/400?random=1",
        likes: 245,
        views: 1200,
        comments: 34,
        saves: 89
      },
      {
        id: 2,
        title: "The Last Letter",
        author: "Ravi Agarwal",
        date: "Sep 15, 2020",
        genre: "Drama",
        tags: ["#emotional", "#family", "#memories"],
        excerpt: "When Sarah finds her grandmother's unfinished letter in the attic, she discovers a family secret that changes everything she thought she knew about her heritage.",
        image: "https://picsum.photos/800/400?random=2",
        likes: 189,
        views: 890,
        comments: 27,
        saves: 65
      }
    ],
    Poems: [
      {
        id: 1,
        title: "Whispers in the Wind",
        author: "Ravi Agarwal",
        date: "Nov 10, 2020",
        genre: "Nature",
        tags: ["#nature", "#peace", "#reflection"],
        excerpt: "The wind carries stories untold, secrets of the earth and sky, whispers of love and loss that dance through the leaves...",
        image: "https://picsum.photos/800/400?random=3",
        likes: 156,
        views: 678,
        comments: 19,
        saves: 43
      }
    ],
    Blogs: [
      {
        id: 1,
        title: "The Art of Storytelling in Digital Age",
        author: "Ravi Agarwal",
        date: "Dec 5, 2020",
        genre: "Writing Tips",
        tags: ["#writing", "#digital", "#storytelling"],
        excerpt: "In today's fast-paced digital world, the art of storytelling has evolved. Here's how modern writers can adapt their craft to engage contemporary audiences...",
        image: "https://picsum.photos/800/400?random=4",
        likes: 234,
        views: 1456,
        comments: 45,
        saves: 78
      }
    ],
    "e-books": [
      {
        id: 1,
        title: "Journey Through Time",
        author: "Ravi Agarwal",
        date: "Jan 20, 2021",
        genre: "Science Fiction",
        tags: ["#scifi", "#timetravel", "#adventure"],
        excerpt: "A complete e-book exploring the possibilities of time travel through the eyes of a young physicist who discovers a way to bend the fabric of time itself.",
        image: "https://picsum.photos/800/400?random=5",
        likes: 567,
        views: 2340,
        comments: 89,
        saves: 234
      }
    ]
  };

  // Testimonial data
  const testimonialData = {
    Approved: [
      {
        id: 1,
        name: "Dheeraj Jadhav",
        avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
        rank: 1270,
        date: "March 6, 2018",
        review: "I recently had the pleasure of reading an incredible story that captivated me from beginning to end. The author's vivid imagination, compelling characters, and intriguing plot all came together to create an unforgettable experience. The story was expertly crafted, with each twist and turn leaving me on the edge"
      },
      {
        id: 2,
        name: "Wade Warren",
        avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
        rank: 1270,
        date: "March 6, 2018",
        review: "I recently had the pleasure of reading an incredible story that captivated me from beginning to end. The author's vivid imagination, compelling characters, and intriguing plot all came together to create an unforgettable experience. The story was expertly crafted, with each twist and turn leaving me on the edge"
      },
      {
        id: 3,
        name: "Jerome Bell",
        avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
        rank: 1270,
        date: "March 6, 2018",
        review: "I recently had the pleasure of reading an incredible story that captivated me from beginning to end. The author's vivid imagination, compelling characters, and intriguing plot all came together to create an unforgettable experience. The story was expertly crafted, with each twist and turn leaving me on the edge"
      }
    ],
    Review: [
      {
        id: 4,
        name: "Sarah Johnson",
        avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
        rank: 890,
        date: "March 8, 2018",
        review: "This is a pending review that needs approval. The content is well-written and engaging, showing great potential for the author's future works."
      },
      {
        id: 5,
        name: "Michael Chen",
        avatar: "https://img.daisyui.com/images/profile/demo/<EMAIL>",
        rank: 1150,
        date: "March 7, 2018",
        review: "Another review awaiting approval. The storytelling technique used here is quite impressive and shows the author's growing skill."
      }
    ]
  };

  const profileData = {
    username: 'Ravi.Agarwal',
    fullName: 'Ravi Agarwal',
    rank: 1800,
    fans: 250,
    faves: 550,
    bio: 'I am a passionate writer with a keen eye for detail, dedicated to exploring the complexities of the human experience through captivating storytelling that leaves a lasting impression.',
    passion: 'Lorem ipsum dolor sit amet consectetur. Enim rhoncus blandit consequat vel nulla at feugiat volutpat.',
    gender: 'Male',
    dob: '04-02-1991',
    mobile: '+012345678',
    occupation: 'Student',
    email: '<EMAIL>',
    location: 'New Delhi',
    language: 'English',
    social: {
      instagram: 'Ravi Agarwal',
      facebook: 'Ravi Agarwal',
      linkedin: 'Ravi Agarwal',
      twitter: 'Ravi Agarwal',
      goodreads: 'Ravi Agarwal',
    }
  };

  return (
    <>
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
      <div className="flex-1 w-full bg-gradient-to-br from-indigo-50 via-white to-cyan-50 min-h-screen relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-cyan-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/5 to-pink-400/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Profile Header */}
      <div className="bg-white/90 backdrop-blur-md p-4 sm:p-6 lg:p-8 border-b border-gray-200/50 text-center shadow-2xl relative overflow-hidden">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/40 to-purple-50/40"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/20 to-transparent"></div>
        <div className="relative z-10">
          <div className="flex justify-center gap-8 sm:gap-12 lg:gap-16 items-center mb-4 sm:mb-6">
            {/* Fans */}
            <div className="group cursor-pointer relative" onClick={() => setCurrentView('fans')}>
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 to-purple-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-95 group-hover:scale-100"></div>
              <div className="relative z-10 p-4 rounded-2xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent group-hover:from-blue-500 group-hover:to-purple-500 transition-all duration-300 group-hover:scale-110">
                  {profileData.fans}
                </div>
                <div className="text-xs sm:text-sm text-gray-600 font-semibold tracking-wider">FANS</div>
                <div className="w-8 sm:w-10 lg:w-12 h-1 bg-gradient-to-r from-[#4A99F8] to-purple-500 mx-auto mt-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 shadow-lg"></div>
              </div>
            </div>

            {/* Circular Profile Image */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse"></div>
              <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 rounded-full overflow-hidden border-4 border-white shadow-2xl group-hover:shadow-3xl transition-all duration-500 group-hover:scale-105 relative z-10">
                <img
                  src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
                  alt={profileData.fullName}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#4A99F8]/30 to-purple-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* Faves */}
            <div className="group cursor-pointer relative" onClick={() => setCurrentView('faves')}>
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/50 to-pink-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-95 group-hover:scale-100"></div>
              <div className="relative z-10 p-4 rounded-2xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-purple-500 group-hover:to-pink-500 transition-all duration-300 group-hover:scale-110">
                  {profileData.faves}
                </div>
                <div className="text-xs sm:text-sm text-gray-600 font-semibold tracking-wider">FAVES</div>
                <div className="w-8 sm:w-10 lg:w-12 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mt-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 shadow-lg"></div>
              </div>
            </div>
          </div>

          {/* Username + Rank */}
          <div className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2 font-medium">Rank {profileData.rank}</div>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-4 sm:mb-6 text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.username}</h2>

          {/* Edit Profile Button */}
          <div className="flex justify-center mt-4 sm:mt-6 mb-6 sm:mb-8 px-4 sm:px-0">
            <button className="group relative flex items-center justify-center gap-2 sm:gap-3 bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-8 sm:px-10 py-3 sm:py-4 rounded-full text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <svg className="w-4 h-4 sm:w-5 sm:h-5 group-hover:rotate-12 transition-transform duration-500 relative z-10" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              <span className="relative z-10">Edit Profile</span>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </div>

          {/* Bio */}
          <p className="text-gray-700 text-xs sm:text-sm max-w-xs sm:max-w-2xl lg:max-w-3xl mx-auto mb-6 sm:mb-8 leading-relaxed bg-white/50 backdrop-blur-sm p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            {profileData.bio}
          </p>

          {/* Tabs */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6 max-w-xs sm:max-w-md lg:max-w-lg mx-auto px-4 sm:px-0">
            <button
              onClick={() => setCurrentView('info')}
              className={`relative group ${currentView === 'info' ? 'bg-[#0A06F4]' : 'bg-[#4A99F8]'} hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">INFO</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-purple-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button
              onClick={() => setCurrentView('skrivee')}
              className={`relative group ${currentView === 'skrivee' ? 'bg-[#0A06F4]' : 'bg-[#4A99F8]'} hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">SKRIVEE</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button
              onClick={() => setCurrentView('feedback')}
              className={`relative group ${currentView === 'feedback' ? 'bg-[#0A06F4]' : 'bg-[#4A99F8]'} hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">FEEDBACK</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full">
        {currentView === 'info' ? (
          /* Info Card */
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 w-full mx-auto shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
            {/* Enhanced Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>
            <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>

            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6 sm:mb-8">
                <h3 className="font-bold text-lg sm:text-xl lg:text-2xl text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.fullName}</h3>
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#2E3A59] to-[#4A99F8] rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transform hover:scale-110 hover:rotate-12 transition-all duration-300 cursor-pointer">
                  <BiGlobe size={16} className="sm:w-5 sm:h-5" />
                </div>
              </div>

              <div className="space-y-4 sm:space-y-6 text-xs sm:text-sm">
                <div className="group">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 text-sm sm:text-base group-hover:text-[#4A99F8] transition-colors duration-300">Passion</strong>
                  <p className="text-gray-700 leading-relaxed bg-gradient-to-r from-blue-50 to-purple-50 p-3 sm:p-4 rounded-lg border-l-4 border-[#4A99F8] hover:shadow-md transition-all duration-300">{profileData.passion}</p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Gender</strong>
                    <p className="text-gray-700 font-medium">{profileData.gender}</p>
                  </div>
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Date of Birth</strong>
                    <p className="text-gray-700 font-medium">{profileData.dob}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Mobile Number</strong>
                    <p className="text-gray-700 font-medium break-all">{profileData.mobile}</p>
                  </div>
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Occupation</strong>
                    <p className="text-gray-700 font-medium">{profileData.occupation}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Email Id</strong>
                    <p className="text-gray-700 font-medium break-all">{profileData.email}</p>
                  </div>
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Location</strong>
                    <p className="text-gray-700 font-medium">{profileData.location}</p>
                  </div>
                </div>

                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Language</strong>
                  <p className="text-gray-700 font-medium">{profileData.language}</p>
                </div>
              </div>
            </div>
          </div>
        ) : currentView === 'fans' ? (
          /* Fans Content */
          <div className="w-full max-w-6xl">
            {/* Fans Header */}
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-4 sm:p-5 lg:p-6 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
              <div className="relative z-10 flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-800">FANS, Ravi Agarwal</h2>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">Sort by</span>
                  <select className="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="popular">Popular</option>
                    <option value="recent">Recent</option>
                    <option value="alphabetical">A-Z</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Fans Grid - 2 cards per row */}
            <div className="max-h-screen overflow-y-auto grid grid-cols-1 md:grid-cols-2 gap-6">
              {fansData.map((fan, index) => (
                <div
                  key={fan.id}
                  onClick={() => navigate('/memberProfile')}
                  className="bg-white/95 backdrop-blur-lg rounded-2xl border border-gray-200/50 p-6 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group cursor-pointer"
                  style={{
                    animation: `slideInUp 0.5s ease-out ${index * 0.1}s forwards`
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>

                  <div className="relative z-10">
                    {/* Fan Header */}
                    <div className="flex items-start gap-4 mb-4">
                      <div className="relative">
                        <img
                          src={fan.avatar}
                          alt={fan.name}
                          className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-lg"
                        />
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <FaHeart className="text-white text-xs" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-bold text-lg text-gray-800">{fan.name}</h3>
                        <p className="text-gray-500 text-sm mb-2">{fan.username}</p>
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">{fan.bio}</p>
                      </div>
                      <div className="relative">
                        <button className="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Fan Stats */}
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.fans}</div>
                        <div className="text-xs text-gray-500 font-semibold">Fans</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.faves}</div>
                        <div className="text-xs text-gray-500 font-semibold">Faves</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.skrivee}</div>
                        <div className="text-xs text-gray-500 font-semibold">Skrivee</div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle follow action here
                          console.log('Follow clicked for:', fan.name);
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition-all duration-300 flex-1"
                      >
                        Follow
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle message action here
                          console.log('Message clicked for:', fan.name);
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Message
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('/memberProfile');
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Profile
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : currentView === 'skrivee' ? (
          /* SKRIVEE Content */
          <div className="w-full mx-auto">
            {/* Blue Background Line - Same width as upper content box */}
            <div className="w-full bg-blue-500 py-1 shadow-lg">
              <div className="px-4">
                <div className="flex justify-evenly items-center w-full">
                  <button
                    onClick={() => setActiveContentTab('Stories')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'Stories'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">📚</span>
                    <span className="text-base">Stories</span>
                  </button>

                  <button
                    onClick={() => setActiveContentTab('Poems')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'Poems'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">✍️</span>
                    <span className="text-base">Poems</span>
                  </button>

                  <button
                    onClick={() => setActiveContentTab('Blogs')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'Blogs'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">📝</span>
                    <span className="text-base">Blogs</span>
                  </button>

                  <button
                    onClick={() => setActiveContentTab('e-books')}
                    className={`px-6 py-2 rounded-lg font-bold flex items-center gap-2 transition-all duration-300 min-w-[120px] justify-center ${
                      activeContentTab === 'e-books'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    <span className="text-lg">📖</span>
                    <span className="text-base">e-books</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Main Content Container with Scrolling */}
            <div className="bg-gradient-to-br from-white via-blue-50 to-indigo-100 h-96 relative overflow-hidden">
              {/* Enhanced Background Patterns */}
              <div className="absolute inset-0 z-0 opacity-10"></div>

              {/* Floating decorative elements */}
              <div className="absolute top-10 right-10 w-20 h-20 bg-gradient-to-br from-blue-300/20 to-indigo-300/20 rounded-full blur-xl animate-pulse"></div>
              <div className="absolute bottom-10 left-10 w-16 h-16 bg-gradient-to-tr from-purple-300/20 to-pink-300/20 rounded-full blur-lg animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-gradient-to-r from-cyan-300/10 to-blue-300/10 rounded-full blur-2xl animate-pulse delay-500"></div>

              {/* Scrollable Content */}
              <div className="relative z-10 h-full overflow-y-auto p-8">
                {/* Content Items - Exact same as MemberProfile */}
                <div className="space-y-8">
                {myContent[activeContentTab]?.map((item) => (
                  <div
                    key={item.id}
                    className="group bg-white/95 backdrop-blur-md border border-gray-200/50 rounded-3xl p-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden transform hover:-translate-y-2 hover:scale-[1.02]"
                  >
                    {/* Advanced Decorative Background Elements */}
                    <div className="absolute -right-16 -top-16 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-indigo-200/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse blur-xl"></div>
                    <div className="absolute -left-16 -bottom-16 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700 delay-100 animate-pulse blur-xl"></div>

                    {/* Floating Particles */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-60 transition-opacity duration-1000">
                      <div className="absolute top-8 right-8 w-2 h-2 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full animate-ping shadow-lg"></div>
                      <div className="absolute top-16 left-16 w-1.5 h-1.5 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-ping delay-500 shadow-lg"></div>
                      <div className="absolute bottom-16 right-16 w-2 h-2 bg-gradient-to-r from-indigo-400 to-blue-400 rounded-full animate-ping delay-1000 shadow-lg"></div>
                    </div>

                    {/* Content Container */}
                    <div className="relative z-10">
                      {/* Author Header - Enhanced */}
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-4">
                          <img
                            src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
                            alt={item.author}
                            className="w-12 h-12 rounded-full border-2 border-blue-300 shadow-md object-cover"
                          />
                          <span className="font-bold text-gray-800 text-lg group-hover:text-blue-700 transition-colors duration-300">{item.author}</span>
                          <span className="text-blue-600 text-xs font-medium bg-blue-50 px-2 py-1 rounded-full ml-2">Author</span>
                        </div>
                        <button className="bg-gradient-to-r from-green-600 to-emerald-700 hover:from-green-700 hover:to-emerald-800 text-white px-8 py-3 rounded-2xl font-semibold transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 shadow-xl hover:shadow-green-500/50">
                          Edit
                        </button>
                      </div>

                      {/* Content Title - Enhanced */}
                      <h2 className="text-xl font-bold text-gray-800 mb-4 leading-tight group-hover:text-blue-700 transition-colors duration-300">
                        {item.title}
                      </h2>

                      {/* Category and Date - Enhanced */}
                      <div className="flex items-center gap-4 mb-4">
                        <span className="bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium border border-blue-100 shadow-sm">
                          {item.genre}
                        </span>
                        <span className="bg-gradient-to-r from-gray-50 to-slate-100 text-gray-700 px-3 py-1 rounded-full text-sm border border-gray-200 shadow-sm">
                          {item.date}
                        </span>
                      </div>

                      {/* Tags - Enhanced */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {item.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="text-blue-600 text-sm hover:text-blue-800 cursor-pointer transition-colors duration-300"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>

                      {/* Excerpt - Enhanced */}
                      <p className="text-gray-600 text-sm leading-relaxed mb-4 group-hover:text-gray-700 transition-colors duration-300">
                        {item.excerpt}
                      </p>

                      {/* Content Image - Enhanced */}
                      <div className="relative rounded-2xl overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-500 transform group-hover:scale-[1.02]">
                        <img
                          src={item.image}
                          alt="Content preview"
                          className="w-full h-48 object-cover transition-transform duration-500 group-hover:scale-105"
                          onError={(e) => {
                            // First fallback - try a different image
                            if (!e.target.dataset.fallback) {
                              e.target.dataset.fallback = "1";
                              const fallbackImages = {
                                'Romance': 'https://picsum.photos/800/400?random=1',
                                'Mystery': 'https://picsum.photos/800/400?random=2',
                                'Fantasy': 'https://picsum.photos/800/400?random=3',
                                'Science': 'https://picsum.photos/800/400?random=4'
                              };
                              e.target.src = fallbackImages[item.genre] || 'https://picsum.photos/800/400?random=5';
                            } else {
                              // Final fallback - colored placeholder
                              e.target.src = `https://via.placeholder.com/800x400/3B82F6/FFFFFF?text=${encodeURIComponent(item.genre)}`;
                            }
                          }}
                          loading="lazy"
                        />
                      </div>

                      {/* Action Icons */}
                      <div className="mt-4 flex justify-around items-center border-t border-gray-200 pt-4">
                        <button className="flex items-center gap-2 text-gray-600 hover:text-blue-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                          <span className="font-semibold text-sm">Like</span>
                        </button>
                        <button className="flex items-center gap-2 text-gray-600 hover:text-green-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                          </svg>
                          <span className="font-semibold text-sm">Comment</span>
                        </button>
                        <button className="flex items-center gap-2 text-gray-600 hover:text-purple-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                          </svg>
                          <span className="font-semibold text-sm">Share</span>
                        </button>
                        <button className="flex items-center gap-2 text-gray-600 hover:text-orange-500 transition-colors duration-300">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                          </svg>
                          <span className="font-semibold text-sm">Save</span>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
                </div>
              </div>
            </div>
          </div>
        ) : currentView === 'faves' ? (
          /* Faves Content */
          <div className="w-full max-w-6xl">
            {/* Faves Header */}
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-4 sm:p-5 lg:p-6 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
              <div className="relative z-10 flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-800">FAVES, Ravi Agarwal</h2>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">Sort by</span>
                  <select className="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="popular">Popular</option>
                    <option value="recent">Recent</option>
                    <option value="alphabetical">A-Z</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Faves Grid - 2 cards per row */}
            <div className="max-h-screen overflow-y-auto grid grid-cols-1 md:grid-cols-2 gap-6">
              {fansData.map((fan, index) => (
                <div
                  key={fan.id}
                  onClick={() => navigate('/memberProfile')}
                  className="bg-white/95 backdrop-blur-lg rounded-2xl border border-gray-200/50 p-6 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group cursor-pointer"
                  style={{
                    animation: `slideInUp 0.5s ease-out ${index * 0.1}s forwards`
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>

                  <div className="relative z-10">
                    {/* Fan Header */}
                    <div className="flex items-start gap-4 mb-4">
                      <div className="relative">
                        <img
                          src={fan.avatar}
                          alt={fan.name}
                          className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-lg"
                        />
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <FaHeart className="text-white text-xs" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-bold text-lg text-gray-800">{fan.name}</h3>
                        <p className="text-gray-500 text-sm mb-2">{fan.username}</p>
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">{fan.bio}</p>
                      </div>
                      <div className="relative">
                        <button className="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Fan Stats */}
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.fans}</div>
                        <div className="text-xs text-gray-500 font-semibold">Fans</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.faves}</div>
                        <div className="text-xs text-gray-500 font-semibold">Faves</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.skrivee}</div>
                        <div className="text-xs text-gray-500 font-semibold">Skrivee</div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle follow action here
                          console.log('Follow clicked for:', fan.name);
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition-all duration-300 flex-1"
                      >
                        Follow
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle message action here
                          console.log('Message clicked for:', fan.name);
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Message
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('/memberProfile');
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Profile
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : currentView === 'feedback' ? (
          /* FEEDBACK Content */
          <div className="w-full mx-auto">


            {/* Blue Background Line with Tabs */}
            <div className="w-full bg-blue-500 py-1 shadow-lg mb-6">
              <div className="px-4">
                <div className="flex justify-center items-center w-full gap-50">
                  <button
                    onClick={() => setActiveTestimonialTab('Approved')}
                    className={`px-8 py-3 rounded-lg font-bold transition-all duration-300 min-w-[150px] ${
                      activeTestimonialTab === 'Approved'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    Approved
                  </button>

                  <button
                    onClick={() => setActiveTestimonialTab('Review')}
                    className={`px-8 py-3 rounded-lg font-bold transition-all duration-300 min-w-[150px] ${
                      activeTestimonialTab === 'Review'
                        ? 'bg-white text-blue-600 shadow-xl scale-110 transform'
                        : 'bg-white/20 text-white hover:bg-white/30 hover:scale-105 hover:shadow-lg'
                    }`}
                  >
                    Review
                  </button>
                </div>
              </div>
            </div>

            {/* Sort by dropdown */}
            <div className="flex justify-end mb-4">
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600">Sort by</span>
                <select className="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="popular">Popular</option>
                  <option value="recent">Recent</option>
                  <option value="oldest">Oldest</option>
                </select>
              </div>
            </div>

            {/* Testimonials Container */}
            <div className="bg-gradient-to-br from-white via-blue-50 to-indigo-100 h-96 relative overflow-hidden rounded-lg">
              {/* Scrollable Content */}
              <div className="relative z-10 h-full overflow-y-auto p-6">
                <div className="space-y-6">
                  {testimonialData[activeTestimonialTab]?.map((testimonial) => (
                    <div
                      key={testimonial.id}
                      className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200/50"
                    >
                      <div className="flex items-start gap-4">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
                        />
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-3">
                              <h4 className="font-bold text-gray-800">{testimonial.name}</h4>
                              <div className="flex items-center gap-2">
                                <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">
                                  Rank {testimonial.rank}
                                </span>
                                <button className="bg-blue-600 text-white px-4 py-1 rounded text-xs font-semibold hover:bg-blue-700 transition-colors">
                                  Follow
                                </button>
                                <button className="bg-gray-100 text-gray-700 px-4 py-1 rounded text-xs font-semibold hover:bg-gray-200 transition-colors">
                                  Message
                                </button>
                              </div>
                            </div>
                            <button className="text-gray-400 hover:text-gray-600 transition-colors">
                              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                              </svg>
                            </button>
                          </div>
                          <p className="text-gray-700 leading-relaxed mb-3">
                            {testimonial.review}
                          </p>
                          <div className="flex items-center justify-between">
                            <p className="text-sm text-gray-500">{testimonial.date}</p>
                            <button className="text-blue-600 text-sm font-semibold hover:text-blue-800 transition-colors">
                              Read More
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>

      {/* Social Links - Only show when not in fans view */}
      {currentView === 'info' && (
        <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 mt-8 sm:mt-10 w-full max-w-6xl mx-auto shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
        <div className="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
        <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>

        <div className="relative z-10">
          <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-4 sm:mb-6 text-center">Connect With Me</h3>
          <div className="space-y-3 sm:space-y-4">
            {[
              {
                icon: <FaInstagram size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-gradient-to-br from-purple-500 to-pink-500',
                label: profileData.social.instagram,
                hoverColor: 'hover:from-purple-600 hover:to-pink-600'
              },
              {
                icon: <FaFacebook size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-blue-600',
                label: profileData.social.facebook,
                hoverColor: 'hover:bg-blue-700'
              },
              {
                icon: <FaLinkedin size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-blue-700',
                label: profileData.social.linkedin,
                hoverColor: 'hover:bg-blue-800'
              },
              {
                icon: <FaTwitter size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-blue-400',
                label: profileData.social.twitter,
                hoverColor: 'hover:bg-blue-500'
              },
              {
                icon: <SiGoodreads size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-amber-700',
                label: profileData.social.goodreads,
                hoverColor: 'hover:bg-amber-800'
              },
            ].map(({ icon, color, label, hoverColor }, idx) => (
              <div key={idx} className="group/item flex items-center gap-3 sm:gap-4 lg:gap-5 p-3 sm:p-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 rounded-lg sm:rounded-xl transition-all duration-300 cursor-pointer hover:shadow-lg transform hover:scale-105">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 ${color} ${hoverColor} rounded-full flex items-center justify-center text-white shadow-lg group-hover/item:shadow-xl transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12`}>
                  {icon}
                </div>
                <span className="text-gray-700 text-sm sm:text-base font-semibold group-hover/item:text-[#4A99F8] transition-colors duration-300 truncate flex-1">{label}</span>
                <div className="ml-auto opacity-0 group-hover/item:opacity-100 transition-opacity duration-300 hidden sm:block">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#4A99F8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
              </div>
            ))}
          </div>
        </div>
        </div>
      )}

      </div>
    </>
  );
}

export default MyProfilePage;