# Skrive Frontend Documentation

## Table of Contents

1. [Project Setup](#project-setup)
2. [Project Structure](#project-structure)
3. [Technologies Used](#technologies-used)
4. [Page Descriptions & Routing](#page-descriptions--routing)

## Project Setup

### Prerequisites

- Node.js (v14.0.0 or later)
- npm (v7.0.0 or later) or yarn (v1.22.0 or later)
- Git

### Installation Steps

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/skrive.git
   cd skrive/Thinkingships/frontend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   # or
   yarn
   ```

3. **Set up environment variables**:
   Create a `.env` file in the frontend directory with the following variables:
   ```
   VITE_API_URL=http://localhost:5000/api
   VITE_SOCKET_URL=http://localhost:5000
   VITE_CLOUDINARY_URL=your_cloudinary_url
   VITE_CLOUDINARY_UPLOAD_PRESET=your_upload_preset
   ```

4. **Start the development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Access the application**:
   Open your browser and navigate to `http://localhost:5173`

## Project Structure

```
frontend/
├── public/                  # Static files
│   ├── images/              # Image assets
│   ├── landing/             # Landing page specific assets
│   ├── logo/                # Logo assets
│   └── sidebar_icons/       # Icons for the sidebar
├── src/
│   ├── action/              # Action creators and API calls
│   │   ├── ProfileHandling.jsx  # Profile-related actions
│   │   ├── SkriveeHandling.jsx  # Content management actions
│   │   ├── TokenHandling.jsx    # Authentication token actions
│   │   └── userActions.js       # User-related actions
│   ├── assets/              # Additional assets
│   ├── component/           # Reusable UI components
│   │   ├── btns/            # Button components
│   │   ├── landing/         # Landing page components
│   │   ├── navbar/          # Navigation components
│   │   ├── resendToken/     # Token handling components
│   │   ├── sidebar/         # Sidebar components
│   │   └── verify/          # Verification components
│   ├── context/             # React context providers
│   │   └── SocketContext.jsx # Socket.io context
│   ├── data/                # Static data and mock data
│   │   └── landing.json     # Landing page content
│   ├── pages/               # Page components
│   │   ├── Auth/            # Authentication pages
│   │   │   ├── Login.jsx    # Login page
│   │   │   └── SignUp.jsx   # Signup page
│   │   ├── authors/         # Author-related pages
│   │   ├── home/            # Home page and related components
│   │   ├── landing/         # Landing page components
│   │   ├── messages/        # Messaging system pages
│   │   ├── pitch/           # Pitch-related pages
│   │   ├── profile/         # Profile pages and components
│   │   │   ├── editProfile/ # Profile editing components
│   │   │   ├── MemberProfile.jsx # View other user's profile
│   │   │   ├── MyProfilePage.jsx # User's own profile
│   │   │   └── Profile.jsx  # Profile page wrapper
│   │   ├── settings/        # Settings pages
│   │   ├── skrivee/         # Skrivee management pages
│   │   └── verification/    # User verification pages
│   ├── reducer/             # Redux reducers
│   │   ├── userProfileReducer.js # Profile state reducer
│   │   └── userReducer.js   # User state reducer
│   ├── styles/              # Global styles
│   │   ├── animations.css   # Animation definitions
│   │   └── profileStyle.css # Profile-specific styles
│   ├── App.jsx              # Main application component with routes
│   ├── App.css              # App-specific styles
│   ├── main.jsx             # Entry point
│   ├── index.css            # Global CSS
│   ├── ProtectedRoute.jsx   # Route protection component
│   └── store.jsx            # Redux store configuration
├── eslint.config.js         # ESLint configuration
├── index.html               # HTML entry point
├── package.json             # Project dependencies and scripts
├── tailwind.config.js       # Tailwind CSS configuration
└── vite.config.js           # Vite configuration
```

## Technologies Used

### Core Technologies
- **React**: Frontend library for building user interfaces
- **Vite**: Next-generation frontend build tool
- **React Router v6**: Routing library for React applications
- **Tailwind CSS**: Utility-first CSS framework
- **Redux**: State management library
- **Axios**: Promise-based HTTP client
- **Socket.IO**: Library for real-time web applications

### UI Libraries & Components
- **Headless UI**: Unstyled, accessible UI components
- **React Icons**: Icon library for React
- **React Dropzone**: File upload component
- **React Markdown**: Markdown rendering component
- **React Toastify**: Notification system
- **React Select**: Enhanced select inputs

### Development Tools
- **ESLint**: Code linting tool
- **Prettier**: Code formatting tool
- **PostCSS**: CSS transformation tool
- **Autoprefixer**: CSS vendor prefixing

## Page Descriptions & Routing

The main routing configuration is defined in `App.jsx` using React Router v6. Below are the key pages and their routes:

### Public Routes

| Route | Component | Description |
|-------|-----------|-------------|
| `/` | `Landing.jsx` | Landing page with hero section, features, testimonials, pricing, and contact form |
| `/login` | `Login.jsx` | User login page with email/password form, social login options, and forgot password link |
| `/signup` | `SignUp.jsx` | Registration page with form validation, role selection, and terms acceptance |
| `/verification` | `Verification.jsx` | Email verification page for new users |
| `/forgot-password` | `ForgotPassword.jsx` | Password reset request page |
| `/reset-password` | `ResetPassword.jsx` | Password reset page with token validation |

### Protected Routes

| Route | Component | Description |
|-------|-----------|-------------|
| `/home` | `Home.jsx` | Dashboard with role-specific content (authors see writing opportunities, publications see submissions) |
| `/profile` | `Profile.jsx` | User profile page with personal info, bio, content showcase, and stats |
| `/profile/edit` | `ProfileEdit.jsx` | Profile editing interface for basic information |
| `/profile/edit/author` | `AuthorEdit.jsx` | Author-specific profile settings (genres, writing samples) |
| `/profile/edit/publication` | `PublicationEdit.jsx` | Publication-specific settings (submission guidelines, audience) |
| `/profile/edit/business` | `BusinessEdit.jsx` | Business-specific settings (industry, content needs) |
| `/memberProfile/:id` | `MemberProfile.jsx` | View other users' profiles with their public information |
| `/authors` | `Authors.jsx` | Browse and search for authors |
| `/authors/ranking` | `AuthorRanking.jsx` | Author rankings based on various metrics |
| `/skrivee` | `Skrivee.jsx` | Content management system for creating/editing/publishing content |
| `/messages` | `Messages.jsx` | Messaging center with conversation list and message threads |
| `/messages/:conversationId` | `Messages.jsx` | Specific conversation view |
| `/pitch` | `Pitch.jsx` | For publications to create requests and review submitted pitches |
| `/author-pitch` | `AuthorPitch.jsx` | For authors to browse requests, submit pitches, and track status |
| `/settings` | `SettingsPage.jsx` | User settings for account, notifications, privacy, and payments |
| `/notifications` | `Notifications.jsx` | User notification center |

### Route Implementation

The main routing setup in `App.jsx`:

```jsx
// App.jsx
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ProtectedRoute } from './ProtectedRoute';
import Layout from './component/Layout';

// Public pages
import Landing from './pages/landing/Landing';
import Login from './pages/Auth/Login';
import SignUp from './pages/Auth/SignUp';
import Verification from './pages/verification/Verification';
import ForgotPassword from './pages/Auth/ForgotPassword';
import ResetPassword from './pages/Auth/ResetPassword';

// Protected pages
import Home from './pages/home/<USER>';
import Profile from './pages/profile/Profile';
import ProfileEdit from './pages/profile/editProfile/ProfileEdit';
import AuthorEdit from './pages/profile/editProfile/AuthorEdit';
import PublicationEdit from './pages/profile/editProfile/PublicationEdit';
import BusinessEdit from './pages/profile/editProfile/BusinessEdit';
import MemberProfile from './pages/profile/MemberProfile';
import Authors from './pages/authors/Authors';
import AuthorRanking from './pages/authors/AuthorRanking';
import Skrivee from './pages/skrivee/Skrivee';
import Messages from './pages/messages/Messages';
import Pitch from './pages/pitch/Pitch';
import AuthorPitch from './pages/pitch/AuthorPitch';
import Settings from './pages/settings/Settings';
import Notifications from './pages/notifications/Notifications';
import NotFound from './pages/NotFound';

function App() {
  return (
    <Router>
      <Routes>
        {/* Public routes */}
        <Route path="/" element={<Landing />} />
        <Route path="/login" element={<Login />} />
        <Route path="/signup" element={<SignUp />} />
        <Route path="/verification" element={<Verification />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/reset-password" element={<ResetPassword />} />
        
        {/* Protected routes with layout */}
        <Route path="/home" element={
          <ProtectedRoute>
            <Layout>
              <Home />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Profile routes */}
        <Route path="/profile" element={
          <ProtectedRoute>
            <Layout>
              <Profile />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/profile/edit" element={
          <ProtectedRoute>
            <Layout>
              <ProfileEdit />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/profile/edit/author" element={
          <ProtectedRoute requiredRole="author">
            <Layout>
              <AuthorEdit />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/profile/edit/publication" element={
          <ProtectedRoute requiredRole="publication">
            <Layout>
              <PublicationEdit />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/profile/edit/business" element={
          <ProtectedRoute requiredRole="business">
            <Layout>
              <BusinessEdit />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/memberProfile/:id" element={
          <ProtectedRoute>
            <Layout>
              <MemberProfile />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Author routes */}
        <Route path="/authors" element={
          <ProtectedRoute>
            <Layout>
              <Authors />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/authors/ranking" element={
          <ProtectedRoute>
            <Layout>
              <AuthorRanking />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Content management */}
        <Route path="/skrivee" element={
          <ProtectedRoute>
            <Layout>
              <Skrivee />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Messaging */}
        <Route path="/messages" element={
          <ProtectedRoute>
            <Layout>
              <Messages />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/messages/:conversationId" element={
          <ProtectedRoute>
            <Layout>
              <Messages />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Pitch system */}
        <Route path="/pitch" element={
          <ProtectedRoute requiredRole="publication">
            <Layout>
              <Pitch />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/author-pitch" element={
          <ProtectedRoute requiredRole="author">
            <Layout>
              <AuthorPitch />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* Settings and notifications */}
        <Route path="/settings" element={
          <ProtectedRoute>
            <Layout>
              <Settings />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/notifications" element={
          <ProtectedRoute>
            <Layout>
              <Notifications />
            </Layout>
          </ProtectedRoute>
        } />
        
        {/* 404 route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;
```

### Route Protection

The `ProtectedRoute` component handles authentication and role-based access:

```jsx
// ProtectedRoute.jsx
import { Navigate } from 'react-router-dom';
import { useAuth } from './context/AuthContext';
import LoadingSpinner from './component/LoadingSpinner';

export const ProtectedRoute = ({ children, requiredRole }) => {
  const { isAuthenticated, loading, user } = useAuth();
  
  if (loading) {
    return <LoadingSpinner />;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }
  
  // Role-based access control
  if (requiredRole && user.role !== requiredRole) {
    return <Navigate to="/unauthorized" />;
  }
  
  return children;
};
```